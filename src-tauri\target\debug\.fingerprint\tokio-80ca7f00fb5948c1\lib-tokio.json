{"rustc": 12488743700189009532, "features": "[\"bytes\", \"default\", \"fs\", \"full\", \"io-std\", \"io-util\", \"libc\", \"macros\", \"mio\", \"net\", \"parking_lot\", \"process\", \"rt\", \"rt-multi-thread\", \"signal\", \"signal-hook-registry\", \"socket2\", \"sync\", \"time\", \"tokio-macros\", \"windows-sys\"]", "declared_features": "[\"bytes\", \"default\", \"fs\", \"full\", \"io-std\", \"io-util\", \"libc\", \"macros\", \"mio\", \"net\", \"parking_lot\", \"process\", \"rt\", \"rt-multi-thread\", \"signal\", \"signal-hook-registry\", \"socket2\", \"sync\", \"test-util\", \"time\", \"tokio-macros\", \"tracing\", \"windows-sys\"]", "target": 9605832425414080464, "profile": 809849551462909832, "path": 13196975297904220650, "deps": [[1812404384583366124, "tokio_macros", false, 4498925480880905798], [1906322745568073236, "pin_project_lite", false, 377786499476956345], [4495526598637097934, "parking_lot", false, 9740506884829540050], [6763978947554154845, "windows_sys", false, 15394918823330232080], [12614995553916589825, "socket2", false, 8204517071945993268], [16066129441945555748, "bytes", false, 14972860757458678711], [16425814114641232863, "mio", false, 5367841881990351608]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tokio-80ca7f00fb5948c1\\dep-lib-tokio", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}