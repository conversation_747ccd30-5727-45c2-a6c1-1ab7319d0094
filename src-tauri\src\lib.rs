// YAugment Rust 版本
// 对应原版 Python 的 YAugment.py

pub mod app_state;
mod commands;
pub mod core;
pub mod gui;
pub mod utils;
pub mod tools;
pub mod version_checker;

use app_state::AppState;
use core::version::Version<PERSON><PERSON><PERSON>;

// 创建共享的 Tauri context 以避免 macOS 上的符号冲突
fn get_tauri_context() -> tauri::Context {
    tauri::generate_context!()
}


/// 启动应用并先显示版本检查窗口
/// 对应原版Python AppManager.run()的完整流程
pub fn run_with_version_check() {
    // 初始化日志（忽略重复初始化错误）
    let _ = tracing_subscriber::fmt::try_init();

    // 创建应用状态
    let app_state = AppState::new().expect("Failed to initialize app state");

    // 启动Tauri应用，配置窗口
    tauri::Builder::default()
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            greet,
            // 窗口控制
            commands::minimize_window,
            commands::toggle_maximize,
            commands::close_window,
            commands::start_window_drag,
            commands::update_window_drag,
            commands::end_window_drag,
            commands::resize_window,
            commands::center_window,
            commands::force_center_window,
            commands::hide_window,
            commands::show_window,
            commands::force_show_window,
            commands::is_window_visible,
            commands::get_window_info,
            commands::resize_window_animated,
            // 配置管理
            commands::get_config,
            commands::get_config_value,
            commands::set_config_value,
            commands::save_config,
            commands::get_editor_type,
            commands::set_editor_type,
            commands::reset_config,
            commands::export_config,
            commands::import_config,
            // 编辑器相关
            commands::get_selected_editor,
            commands::select_editor,
            commands::get_editor_status,
            commands::detect_editors,
            // 应用状态
            commands::is_first_run,
            // 重置功能
            commands::reset_augment,
            commands::get_reset_status,
            commands::force_clear_reset_status,
            // Cursor 工具
            commands::cursor::cursor_modify_telemetry_ids,
            commands::cursor::cursor_clean_augment_data,
            commands::cursor::cursor_clean_workspace_storage,
            commands::cursor::cursor_count_augment_records,
            commands::cursor::cursor_terminate_processes,
            commands::cursor::cursor_full_reset,
            // VSCode 工具
            commands::vscode::vscode_modify_telemetry_ids,
            commands::vscode::vscode_clean_augment_data,
            commands::vscode::vscode_clean_workspace_storage,
            commands::vscode::vscode_count_augment_records,
            commands::vscode::vscode_terminate_processes,
            commands::vscode::vscode_full_reset,
            // JetBrains 工具
            commands::jetbrains::jetbrains_modify_ids,
            commands::jetbrains::jetbrains_lock_files,
            commands::jetbrains::jetbrains_unlock_files,
            commands::jetbrains::jetbrains_check_status,
            commands::jetbrains::jetbrains_is_installed,
            commands::jetbrains::jetbrains_get_info,
            commands::jetbrains::jetbrains_terminate_processes,
            commands::jetbrains::jetbrains_check_running_processes,
            commands::jetbrains::jetbrains_full_reset,
            // 邮箱功能
            commands::generate_email,
            commands::generate_temp_email,
            commands::get_verification_code,
            commands::get_verification_status,
            commands::test_email_connection,
            commands::stop_verification_code,
            commands::copy_text_to_clipboard,
            // 账号管理
            commands::query_augment_account,
            // 新增账号管理功能
            commands::account::verify_session_token,
            commands::account::get_portal_token,
            commands::account::query_account_with_portal,
            commands::account::query_account_with_portal_and_update_time,
            commands::account::get_saved_accounts,
            commands::account::get_saved_account_by_email,
            commands::account::remove_saved_account,
            commands::account::get_machine_uuid,
            commands::account::switch_to_community_plan,
            // 版本检查
            commands::check_version,
            commands::get_disclaimer_info,
            commands::agree_disclaimer,
            commands::mark_verification_completed,
            commands::get_gzh_qrcode,
            commands::verify_code,
            commands::verify_vip_qq,
            commands::generate_verification_qr,
            commands::start_qq_status_check,
            commands::check_qr_status,
            commands::get_about_info,
            commands::get_config_help_url,
            commands::get_token_help_url,
            commands::get_expiry_status,
            commands::stop_expiry_check,
            // 网络优化器
            commands::get_network_optimizer_config,
            commands::set_network_optimizer_config,
            commands::test_proxy_connection,
            commands::test_all_domains,
            commands::execute_network_optimization,
            commands::clear_network_optimization,
            commands::start_auto_optimization,
            commands::stop_auto_optimization,
            commands::get_network_optimization_status,
            commands::check_curl_available,
            commands::check_admin_privileges_cmd,
            // 日志
            commands::log_message,
            // 应用控制
            commands::exit_app,
            commands::open_url,
            commands::start_main_app,
            commands::resize_window,
            commands::center_window,
            commands::hide_window,
            commands::show_window,
        ])
        .run(get_tauri_context())
        .expect("error while running tauri application");
}

/// 使用已验证的版本检查器启动应用
/// 对应原版Python AppManager.run()中版本检查通过后的部分
pub fn run_with_version_checker(version_checker: VersionChecker) {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("版本检查通过，启动主程序...");

    // 创建包含版本检查器的应用状态 - 对应原版Python第214行
    let app_state = AppState::new_with_version_checker(version_checker).expect("Failed to initialize app state with version checker");

    // 启动Tauri应用
    tauri::Builder::default()
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            greet,
        ])
        .run(get_tauri_context())
        .expect("error while running tauri application");
}

// 临时保留的示例命令
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // ============================================================================
    // 应用启动流程 - 完全对应原版Python的AppManager.run()
    // ============================================================================

    // 这个函数保留用于向后兼容，但实际应该使用main.rs启动程序
    eprintln!("警告: 直接调用run()跳过了版本检查，请使用main.rs启动程序");

    // 创建应用状态
    let app_state = AppState::new().expect("Failed to initialize app state");

    tauri::Builder::default()
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            greet,
            // 窗口控制
            commands::minimize_window,
            commands::toggle_maximize,
            commands::close_window,
            commands::start_window_drag,
            commands::update_window_drag,
            commands::end_window_drag,
            commands::resize_window,
            commands::center_window,
            commands::hide_window,
            commands::show_window,
            commands::get_window_info,
            commands::resize_window_animated,
            // 配置管理
            commands::get_config,
            commands::get_config_value,
            commands::set_config_value,
            commands::save_config,
            commands::get_editor_type,
            commands::set_editor_type,
            commands::reset_config,
            commands::export_config,
            commands::import_config,
            // 编辑器相关
            commands::get_selected_editor,
            commands::select_editor,
            commands::get_editor_status,
            commands::detect_editors,
            // 应用状态
            commands::is_first_run,
            // 重置功能
            commands::reset_augment,
            commands::get_reset_status,
            commands::force_clear_reset_status,
            // Cursor 工具
            commands::cursor::cursor_modify_telemetry_ids,
            commands::cursor::cursor_clean_augment_data,
            commands::cursor::cursor_clean_workspace_storage,
            commands::cursor::cursor_count_augment_records,
            commands::cursor::cursor_terminate_processes,
            commands::cursor::cursor_full_reset,
            // VSCode 工具
            commands::vscode::vscode_modify_telemetry_ids,
            commands::vscode::vscode_clean_augment_data,
            commands::vscode::vscode_clean_workspace_storage,
            commands::vscode::vscode_count_augment_records,
            commands::vscode::vscode_terminate_processes,
            commands::vscode::vscode_full_reset,
            // JetBrains 工具
            commands::jetbrains::jetbrains_modify_ids,
            commands::jetbrains::jetbrains_lock_files,
            commands::jetbrains::jetbrains_unlock_files,
            commands::jetbrains::jetbrains_check_status,
            commands::jetbrains::jetbrains_is_installed,
            commands::jetbrains::jetbrains_get_info,
            commands::jetbrains::jetbrains_terminate_processes,
            commands::jetbrains::jetbrains_check_running_processes,
            commands::jetbrains::jetbrains_full_reset,
            // 邮箱功能
            commands::generate_email,
            commands::generate_temp_email,
            commands::get_verification_code,
            commands::get_verification_status,
            commands::test_email_connection,
            commands::stop_verification_code,
            commands::copy_text_to_clipboard,
            // 账号管理
            commands::query_augment_account,
            // 新增账号管理功能
            commands::account::verify_session_token,
            commands::account::get_portal_token,
            commands::account::query_account_with_portal,
            commands::account::query_account_with_portal_and_update_time,
            commands::account::get_saved_accounts,
            commands::account::get_saved_account_by_email,
            commands::account::remove_saved_account,
            commands::account::get_machine_uuid,
            // 版本检查
            commands::check_version,
            commands::get_disclaimer_info,
            commands::agree_disclaimer,
            commands::mark_verification_completed,
            commands::is_verification_completed,
            commands::get_gzh_qrcode,
            commands::verify_code,
            commands::verify_vip_qq,
            commands::generate_verification_qr,
            commands::start_qq_status_check,
            commands::check_qr_status,
            commands::get_about_info,
            commands::get_config_help_url,
            commands::get_token_help_url,
            commands::get_expiry_status,
            commands::stop_expiry_check,
            // 网络优化器
            commands::get_network_optimizer_config,
            commands::set_network_optimizer_config,
            commands::test_proxy_connection,
            commands::test_all_domains,
            commands::execute_network_optimization,
            commands::clear_network_optimization,
            commands::start_auto_optimization,
            commands::stop_auto_optimization,
            commands::get_network_optimization_status,
            commands::check_curl_available,
            commands::check_admin_privileges_cmd,
            // 日志
            commands::log_message,
            // 应用控制
            commands::exit_app,
            commands::open_url,
            commands::start_main_app,
            commands::resize_window,
            commands::center_window,
            commands::force_center_window,
            commands::hide_window,
            commands::show_window,
            commands::force_show_window,
            commands::is_window_visible,
        ])
        .run(get_tauri_context())
        .expect("error while running tauri application");
}
