{"rustc": 12488743700189009532, "features": "[\"default\", \"multithread\", \"rayon\"]", "declared_features": "[\"apple-app-store\", \"apple-sandbox\", \"c-interface\", \"debug\", \"default\", \"linux-netdevs\", \"linux-tmpfs\", \"multithread\", \"rayon\", \"serde\", \"unknown-ci\"]", "target": 9076746759397982909, "profile": 15657897354478470176, "path": 18371524396371536851, "deps": [[2828590642173593838, "cfg_if", false, 16048148996252698940], [3722963349756955755, "once_cell", false, 3001542277175768057], [4684437522915235464, "libc", false, 8772519399186178916], [8695090098768911186, "windows", false, 8152974230059362276], [10697383615564341592, "rayon", false, 18389683901626638827], [11969445987175445311, "ntapi", false, 13515479743548746827]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sysinfo-f02660589432ce8f\\dep-lib-sysinfo", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}