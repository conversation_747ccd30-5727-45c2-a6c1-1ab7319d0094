{"rustc": 12488743700189009532, "features": "[\"default\", \"public_suffix\", \"publicsuffix\"]", "declared_features": "[\"default\", \"indexmap\", \"log_secure_cookie_values\", \"preserve_order\", \"public_suffix\", \"publicsuffix\", \"wasm-bindgen\"]", "target": 12412619239536776736, "profile": 15657897354478470176, "path": 3214352621771594114, "deps": [[505596520502798227, "publicsuffix", false, 819143242294724501], [2779053297469913730, "cookie", false, 16841455798196774950], [3150220818285335163, "url", false, 6062674917897409888], [3774582549255978861, "idna", false, 9957578016567664088], [5986029879202738730, "log", false, 6744974836633744950], [9689903380558560274, "serde", false, 12662985874143964422], [12409575957772518135, "time", false, 2131033935219245394], [15367738274754116744, "serde_json", false, 14741482141302888685], [16257276029081467297, "serde_derive", false, 982129096129896074]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\cookie_store-4d66761e9cdb2660\\dep-lib-cookie_store", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}