{"rustc": 12488743700189009532, "features": "[\"default\", \"hmac\", \"password-hash\", \"sha2\", \"simple\"]", "declared_features": "[\"default\", \"hmac\", \"parallel\", \"password-hash\", \"rayon\", \"sha1\", \"sha2\", \"simple\", \"std\"]", "target": 9229284490985355380, "profile": 15657897354478470176, "path": 18385634971022475543, "deps": [[7929110456145265570, "password_hash", false, 9788312794781387905], [9209347893430674936, "hmac", false, 13475496206054761251], [9857275760291862238, "sha2", false, 17568200356493912515], [17475753849556516473, "digest", false, 2215462883847041727]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pbkdf2-a6d522657e51f85d\\dep-lib-pbkdf2", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}