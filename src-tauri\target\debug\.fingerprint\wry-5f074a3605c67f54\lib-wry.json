{"rustc": 12488743700189009532, "features": "[\"drag-drop\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"os-webview\", \"protocol\", \"soup3\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"devtools\", \"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"mac-proxy\", \"os-webview\", \"protocol\", \"serde\", \"soup3\", \"tracing\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "target": 2463569863749872413, "profile": 11987593577772629573, "path": 1195496608286274052, "deps": [[2013030631243296465, "webview2_com", false, 12481653937944749380], [3334271191048661305, "windows_version", false, 511316909870963320], [3722963349756955755, "once_cell", false, 3001542277175768057], [4143744114649553716, "raw_window_handle", false, 5439867478843892220], [5628259161083531273, "windows_core", false, 11162791511167397288], [7606335748176206944, "dpi", false, 17045838260048947188], [9010263965687315507, "http", false, 3514203428925704760], [9141053277961803901, "build_script_build", false, 10604883173575756763], [10806645703491011684, "thiserror", false, 14032229823463785456], [11989259058781683633, "dunce", false, 1785783859457516269], [14585479307175734061, "windows", false, 1621046816081544285], [16727543399706004146, "cookie", false, 17007315089155735358]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-5f074a3605c67f54\\dep-lib-wry", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}