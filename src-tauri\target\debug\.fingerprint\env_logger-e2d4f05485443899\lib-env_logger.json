{"rustc": 12488743700189009532, "features": "[\"auto-color\", \"color\", \"default\", \"humantime\", \"regex\"]", "declared_features": "[\"auto-color\", \"color\", \"default\", \"humantime\", \"regex\"]", "target": 12068211720450992361, "profile": 15657897354478470176, "path": 4638069381481347582, "deps": [[490811470990815549, "is_terminal", false, 12610989359358171961], [5986029879202738730, "log", false, 6744974836633744950], [9451456094439810778, "regex", false, 10818032451952740065], [12902659978838094914, "termcolor", false, 3490487639485783499], [14164303037627012804, "humantime", false, 15840619833373555384]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\env_logger-e2d4f05485443899\\dep-lib-env_logger", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}