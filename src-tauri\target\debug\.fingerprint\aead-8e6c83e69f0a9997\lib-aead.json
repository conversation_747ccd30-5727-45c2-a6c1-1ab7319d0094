{"rustc": 12488743700189009532, "features": "[\"alloc\", \"getrandom\", \"rand_core\"]", "declared_features": "[\"alloc\", \"arrayvec\", \"blobby\", \"bytes\", \"default\", \"dev\", \"getrandom\", \"heapless\", \"rand_core\", \"std\", \"stream\"]", "target": 6415113071054268027, "profile": 15657897354478470176, "path": 12781359159548661413, "deps": [[2352660017780662552, "crypto_common", false, 2801516405637167640], [10520923840501062997, "generic_array", false, 17260544749590778196]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\aead-8e6c83e69f0a9997\\dep-lib-aead", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}