{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 15172315466741368323, "profile": 15657897354478470176, "path": 17940439289104022105, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\overload-20a7e9873621d470\\dep-lib-overload", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}