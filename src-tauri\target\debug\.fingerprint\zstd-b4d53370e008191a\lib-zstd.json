{"rustc": 12488743700189009532, "features": "[\"arrays\", \"default\", \"legacy\", \"zdict_builder\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"legacy\", \"no_asm\", \"pkg-config\", \"thin\", \"wasm\", \"zdict_builder\", \"zstdmt\"]", "target": 10618060358209793422, "profile": 15657897354478470176, "path": 8066085736147559442, "deps": [[11328745298441753262, "zstd_safe", false, 17489738695051939813]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zstd-b4d53370e008191a\\dep-lib-zstd", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}