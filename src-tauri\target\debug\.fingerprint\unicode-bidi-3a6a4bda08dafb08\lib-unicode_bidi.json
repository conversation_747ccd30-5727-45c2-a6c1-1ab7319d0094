{"rustc": 12488743700189009532, "features": "[\"default\", \"hardcoded-data\", \"std\"]", "declared_features": "[\"bench_it\", \"default\", \"flame\", \"flame_it\", \"flamer\", \"hardcoded-data\", \"serde\", \"smallvec\", \"std\", \"unstable\", \"with_serde\"]", "target": 15602362298795533203, "profile": 15657897354478470176, "path": 4960337671709048039, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\unicode-bidi-3a6a4bda08dafb08\\dep-lib-unicode_bidi", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}