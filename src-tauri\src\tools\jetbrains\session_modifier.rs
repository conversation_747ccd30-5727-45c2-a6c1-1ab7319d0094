/// JetBrains SessionID 修改工具
/// 
/// 负责修改JetBrains IDE的SessionID配置
/// 对应 AugmentCode-Free-master 的 jetbrains_manager.py 中的SessionID功能

use std::fs;
use std::path::{Path, PathBuf};
use quick_xml::events::{Event, BytesStart};
use quick_xml::{Reader, Writer};
use uuid::Uuid;
use crate::utils::backup::create_backup;
use super::paths::get_jetbrains_config_dir;

/// SessionID修改结果结构体
#[derive(Debug, Clone, serde::Serialize)]
pub struct SessionModifyResult {
    pub session_id_backup_paths: Vec<String>,
    pub old_session_ids: Vec<String>,
    pub new_session_id: String,
    pub configs_updated: usize,
    pub configs_failed: usize,
    pub success: bool,
}

/// 生成新的SessionID
/// 
/// Returns:
///     String: 新的UUID字符串
pub fn generate_session_id() -> String {
    Uuid::new_v4().to_string()
}

/// 查找所有JetBrains产品配置目录
/// 
/// Returns:
///     Vec<PathBuf>: 所有找到的产品配置目录列表
pub fn find_jetbrains_config_dirs() -> Vec<PathBuf> {
    let mut config_dirs = Vec::new();
    
    if let Some(jetbrains_base) = get_jetbrains_config_dir() {
        if let Ok(entries) = fs::read_dir(&jetbrains_base) {
            for entry in entries.flatten() {
                let path = entry.path();
                if path.is_dir() {
                    // 检查是否是JetBrains产品目录
                    let dir_name = path.file_name()
                        .and_then(|n| n.to_str())
                        .unwrap_or("");

                    // 常见的JetBrains产品前缀
                    let jetbrains_products = [
                        "PyCharm", "IntelliJIdea", "WebStorm", "PhpStorm",
                        "CLion", "DataGrip", "GoLand", "RubyMine",
                        "AppCode", "AndroidStudio", "Rider", "DataSpell"
                    ];

                    if jetbrains_products.iter().any(|&product| dir_name.starts_with(product)) {
                        config_dirs.push(path);
                    }
                }
            }
        }
    }
    
    config_dirs
}

/// 修改单个JetBrains产品的SessionID
/// 
/// Args:
///     config_dir: JetBrains产品配置目录
///     session_id: 新的SessionID
///     
/// Returns:
///     Result<(String, String), String>: (旧SessionID, 备份路径) 或错误信息
pub fn modify_single_session_id(config_dir: &Path, session_id: &str) -> Result<(String, String), String> {
    let options_dir = config_dir.join("options");
    let ide_general_file = options_dir.join("ide.general.xml");
    
    // 检查字体配置文件，如果存在则跳过以保护用户设置
    let font_config_file = options_dir.join("font.options.xml");
    if font_config_file.exists() {
        return Ok((String::new(), String::new()));
    }
    
    // 确保options目录存在
    fs::create_dir_all(&options_dir)
        .map_err(|e| format!("创建options目录失败: {}", e))?;
    
    let mut old_session_id = String::new();
    let backup_path = if ide_general_file.exists() {
        // 先读取旧的SessionID
        if let Ok(content) = fs::read_to_string(&ide_general_file) {
            old_session_id = extract_session_id_from_xml(&content);
        }
        
        // 创建备份
        create_backup(&ide_general_file)
            .map_err(|e| format!("创建备份失败: {}", e))?
            .to_string_lossy()
            .to_string()
    } else {
        String::new()
    };
    
    // 修改或创建XML配置
    let xml_content = if ide_general_file.exists() {
        // 读取现有配置并修改
        let content = fs::read_to_string(&ide_general_file)
            .map_err(|e| format!("读取配置文件失败: {}", e))?;
        modify_xml_session_id(&content, session_id)?
    } else {
        // 创建新的配置文件
        create_new_xml_config(session_id)
    };
    
    // 写入文件
    fs::write(&ide_general_file, xml_content)
        .map_err(|e| format!("写入配置文件失败: {}", e))?;

    Ok((old_session_id, backup_path))
}

/// 从XML内容中提取SessionID
/// 
/// Args:
///     xml_content: XML文件内容
///     
/// Returns:
///     String: 提取的SessionID，如果不存在则返回空字符串
fn extract_session_id_from_xml(xml_content: &str) -> String {
    let mut reader = Reader::from_str(xml_content);
    reader.trim_text(true);
    
    let mut buf = Vec::new();
    let mut in_general_settings = false;
    
    loop {
        match reader.read_event_into(&mut buf) {
            Ok(Event::Start(ref e)) => {
                if e.name().as_ref() == b"component" {
                    for attr in e.attributes() {
                        if let Ok(attr) = attr {
                            if attr.key.as_ref() == b"name" && attr.value.as_ref() == b"GeneralSettings" {
                                in_general_settings = true;
                                break;
                            }
                        }
                    }
                } else if in_general_settings && e.name().as_ref() == b"property" {
                    let mut name = String::new();
                    let mut value = String::new();
                    
                    for attr in e.attributes() {
                        if let Ok(attr) = attr {
                            match attr.key.as_ref() {
                                b"name" => {
                                    if let Ok(s) = std::str::from_utf8(&attr.value) {
                                        name = s.to_string();
                                    }
                                }
                                b"value" => {
                                    if let Ok(s) = std::str::from_utf8(&attr.value) {
                                        value = s.to_string();
                                    }
                                }
                                _ => {}
                            }
                        }
                    }
                    
                    if name == "augment.session.id" {
                        return value;
                    }
                }
            }
            Ok(Event::End(ref e)) => {
                if e.name().as_ref() == b"component" {
                    in_general_settings = false;
                }
            }
            Ok(Event::Eof) => break,
            Err(e) => {
                eprintln!("警告: XML解析错误: {}", e);
                break;
            }
            _ => {}
        }
        buf.clear();
    }
    
    String::new()
}

/// 修改XML内容中的SessionID
/// 
/// Args:
///     xml_content: 原始XML内容
///     session_id: 新的SessionID
///     
/// Returns:
///     Result<String, String>: 修改后的XML内容或错误信息
fn modify_xml_session_id(xml_content: &str, session_id: &str) -> Result<String, String> {
    let mut reader = Reader::from_str(xml_content);
    reader.trim_text(true);
    
    let mut writer = Writer::new(Vec::new());
    let mut buf = Vec::new();
    let mut in_general_settings = false;
    let mut session_id_found = false;
    let mut general_settings_depth = 0;
    
    loop {
        match reader.read_event_into(&mut buf) {
            Ok(Event::Start(ref e)) => {
                if e.name().as_ref() == b"component" {
                    for attr in e.attributes() {
                        if let Ok(attr) = attr {
                            if attr.key.as_ref() == b"name" && attr.value.as_ref() == b"GeneralSettings" {
                                in_general_settings = true;
                                general_settings_depth = 1;
                                break;
                            }
                        }
                    }
                } else if in_general_settings {
                    general_settings_depth += 1;
                    
                    if e.name().as_ref() == b"property" {
                        let mut is_session_property = false;
                        let mut new_elem = BytesStart::new("property");
                        
                        for attr in e.attributes() {
                            if let Ok(attr) = attr {
                                match attr.key.as_ref() {
                                    b"name" => {
                                        if attr.value.as_ref() == b"augment.session.id" {
                                            is_session_property = true;
                                            session_id_found = true;
                                        }
                                        new_elem.push_attribute(attr);
                                    }
                                    b"value" => {
                                        if is_session_property {
                                            new_elem.push_attribute(("value", session_id));
                                        } else {
                                            new_elem.push_attribute(attr);
                                        }
                                    }
                                    _ => {
                                        new_elem.push_attribute(attr);
                                    }
                                }
                            }
                        }
                        
                        writer.write_event(Event::Start(new_elem))
                            .map_err(|e| format!("写入XML失败: {}", e))?;
                        continue;
                    }
                }
                
                writer.write_event(Event::Start(e.clone()))
                    .map_err(|e| format!("写入XML失败: {}", e))?;
            }
            Ok(Event::End(ref e)) => {
                if in_general_settings {
                    general_settings_depth -= 1;
                    
                    if e.name().as_ref() == b"component" && general_settings_depth == 0 {
                        // 在GeneralSettings组件结束前添加SessionID属性（如果还没有）
                        if !session_id_found {
                            let mut property_elem = BytesStart::new("property");
                            property_elem.push_attribute(("name", "augment.session.id"));
                            property_elem.push_attribute(("value", session_id));
                            
                            writer.write_event(Event::Empty(property_elem))
                                .map_err(|e| format!("写入XML失败: {}", e))?;
                        }
                        
                        in_general_settings = false;
                    }
                }
                
                writer.write_event(Event::End(e.clone()))
                    .map_err(|e| format!("写入XML失败: {}", e))?;
            }
            Ok(Event::Eof) => break,
            Ok(event) => {
                writer.write_event(event)
                    .map_err(|e| format!("写入XML失败: {}", e))?;
            }
            Err(e) => return Err(format!("XML解析错误: {}", e)),
        }
        buf.clear();
    }
    
    String::from_utf8(writer.into_inner())
        .map_err(|e| format!("UTF-8转换失败: {}", e))
}

/// 创建新的XML配置文件
/// 
/// Args:
///     session_id: SessionID值
///     
/// Returns:
///     String: 新的XML配置内容
fn create_new_xml_config(session_id: &str) -> String {
    format!(
        r#"<?xml version="1.0" encoding="UTF-8"?>
<application>
  <component name="GeneralSettings">
    <property name="augment.session.id" value="{}" />
  </component>
</application>"#,
        session_id
    )
}

/// 修改所有JetBrains产品的SessionID
///
/// Args:
///     custom_session_id: 自定义SessionID，如果为None则自动生成
///
/// Returns:
///     Result<SessionModifyResult, String>: 修改结果或错误信息
pub fn modify_all_jetbrains_session_ids(custom_session_id: Option<String>) -> Result<SessionModifyResult, String> {
    println!("开始修改JetBrains SessionID...");

    let session_id = custom_session_id.unwrap_or_else(generate_session_id);
    let config_dirs = find_jetbrains_config_dirs();

    if config_dirs.is_empty() {
        return Err("未找到任何JetBrains产品配置".to_string());
    }

    let mut result = SessionModifyResult {
        session_id_backup_paths: Vec::new(),
        old_session_ids: Vec::new(),
        new_session_id: session_id.clone(),
        configs_updated: 0,
        configs_failed: 0,
        success: false,
    };

    for config_dir in config_dirs {
        let dir_name = config_dir.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("Unknown");

        match modify_single_session_id(&config_dir, &session_id) {
            Ok((old_id, backup_path)) => {
                result.configs_updated += 1;
                if !old_id.is_empty() {
                    result.old_session_ids.push(old_id);
                }
                if !backup_path.is_empty() {
                    result.session_id_backup_paths.push(backup_path);
                }
                println!("SessionID更新成功: {}", dir_name);
            }
            Err(e) => {
                result.configs_failed += 1;
                eprintln!("SessionID更新失败 {}: {}", dir_name, e);
            }
        }
    }

    result.success = result.configs_updated > 0;

    if result.success {
        println!("JetBrains SessionID修改完成: {} 个成功, {} 个失败",
                result.configs_updated,
                result.configs_failed);
    } else {
        return Err("所有JetBrains产品的SessionID修改都失败了".to_string());
    }

    Ok(result)
}

/// 检查JetBrains SessionID状态
///
/// Returns:
///     Result<serde_json::Value, String>: SessionID状态信息或错误信息
pub fn check_jetbrains_session_status() -> Result<serde_json::Value, String> {
    let config_dirs = find_jetbrains_config_dirs();
    let mut status_info = Vec::new();

    for config_dir in config_dirs {
        let dir_name = config_dir.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("Unknown")
            .to_string();

        let ide_general_file = config_dir.join("options").join("ide.general.xml");
        let mut session_id = String::new();
        let mut config_exists = false;

        if ide_general_file.exists() {
            config_exists = true;
            if let Ok(content) = fs::read_to_string(&ide_general_file) {
                session_id = extract_session_id_from_xml(&content);
            }
        }

        status_info.push(serde_json::json!({
            "product": dir_name,
            "config_path": config_dir.to_string_lossy(),
            "config_exists": config_exists,
            "session_id": session_id,
            "has_session_id": !session_id.is_empty()
        }));
    }

    Ok(serde_json::json!({
        "total_products": status_info.len(),
        "products": status_info
    }))
}
