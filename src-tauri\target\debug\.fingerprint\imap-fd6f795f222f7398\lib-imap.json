{"rustc": 12488743700189009532, "features": "[\"default\", \"native-tls\", \"tls\"]", "declared_features": "[\"default\", \"native-tls\", \"tls\"]", "target": 5270262826481891391, "profile": 15657897354478470176, "path": 15227660913645724353, "deps": [[3624256500975602054, "imap_proto", false, 1508771719924872920], [6503212141020726168, "bufstream", false, 11991509861231892499], [9451456094439810778, "regex", false, 10818032451952740065], [9897246384292347999, "chrono", false, 13192091948753435637], [10578342194672938417, "nom", false, 10235370106716711013], [16785601910559813697, "native_tls", false, 10286770631657771184], [17282734725213053079, "base64", false, 2847730798749856847], [17917672826516349275, "lazy_static", false, 10688309873950042378]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\imap-fd6f795f222f7398\\dep-lib-imap", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}