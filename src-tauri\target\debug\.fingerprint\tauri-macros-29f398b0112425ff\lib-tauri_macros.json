{"rustc": 12488743700189009532, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 9988043381905268180, "deps": [[2671782512663819132, "tauri_utils", false, 5376160995092444840], [3060637413840920116, "proc_macro2", false, 6584666464481727759], [4974441333307933176, "syn", false, 11021406926825034584], [13077543566650298139, "heck", false, 9099268929377437097], [14455244907590647360, "tauri_codegen", false, 17527681084539004959], [17990358020177143287, "quote", false, 17007783491292180124]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-29f398b0112425ff\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}