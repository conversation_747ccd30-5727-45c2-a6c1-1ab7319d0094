{"rustc": 12488743700189009532, "features": "[\"custom-protocol\", \"default\"]", "declared_features": "[\"custom-protocol\", \"default\"]", "target": 5408242616063297496, "profile": 7409704062750675268, "path": 13767053534773805487, "deps": [[10806952569398136823, "tauri_build", false, 17564123906005242636]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\yaugment-a2295300e3ab5f68\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}