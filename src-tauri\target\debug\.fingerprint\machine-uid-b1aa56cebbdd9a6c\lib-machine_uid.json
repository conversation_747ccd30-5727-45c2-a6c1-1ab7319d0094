{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 11670804123360568223, "profile": 15657897354478470176, "path": 4880575677530666205, "deps": [[1184807431810893920, "winreg", false, 3063425349104878906]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\machine-uid-b1aa56cebbdd9a6c\\dep-lib-machine_uid", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}