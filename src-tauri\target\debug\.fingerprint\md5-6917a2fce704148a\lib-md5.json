{"rustc": 12488743700189009532, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"std\"]", "target": 16521573572710815818, "profile": 15657897354478470176, "path": 2777707047886767398, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\md5-6917a2fce704148a\\dep-lib-md5", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}