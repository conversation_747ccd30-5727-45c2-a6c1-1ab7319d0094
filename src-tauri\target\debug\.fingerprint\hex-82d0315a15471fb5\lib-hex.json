{"rustc": 12488743700189009532, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"serde\", \"std\"]", "target": 4242469766639956503, "profile": 15657897354478470176, "path": 10558574638986701657, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hex-82d0315a15471fb5\\dep-lib-hex", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}