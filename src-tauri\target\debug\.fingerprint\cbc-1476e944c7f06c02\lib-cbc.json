{"rustc": 12488743700189009532, "features": "[\"block-padding\", \"default\"]", "declared_features": "[\"alloc\", \"block-padding\", \"default\", \"std\", \"zeroize\"]", "target": 5103841873489430697, "profile": 15657897354478470176, "path": 11365119834447878180, "deps": [[7916416211798676886, "cipher", false, 13512874754309562015]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\cbc-1476e944c7f06c02\\dep-lib-cbc", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}