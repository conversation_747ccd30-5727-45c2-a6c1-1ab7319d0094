{"rustc": 12488743700189009532, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 11584472151622172371, "deps": [[2671782512663819132, "tauri_utils", false, 5376160995092444840], [4899080583175475170, "semver", false, 3007983211784184934], [6913375703034175521, "schemars", false, 2990764407818227964], [7170110829644101142, "json_patch", false, 4329366685579714835], [9689903380558560274, "serde", false, 6420858207137877299], [12714016054753183456, "tauri_winres", false, 15190197238532978027], [13077543566650298139, "heck", false, 9099268929377437097], [13475171727366188400, "cargo_toml", false, 13274597785779570284], [13625485746686963219, "anyhow", false, 17047174968382787859], [15367738274754116744, "serde_json", false, 14282978953253237938], [15609422047640926750, "toml", false, 12566317272855078969], [15622660310229662834, "walkdir", false, 13774418868814605573], [16928111194414003569, "dirs", false, 11644794514588536626], [17155886227862585100, "glob", false, 4678781086715643041]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-e89916feaaa422a3\\dep-lib-tauri_build", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}