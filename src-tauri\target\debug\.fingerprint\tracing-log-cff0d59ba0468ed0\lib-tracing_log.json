{"rustc": 12488743700189009532, "features": "[\"log-tracer\", \"std\"]", "declared_features": "[\"ahash\", \"default\", \"interest-cache\", \"log-tracer\", \"lru\", \"std\"]", "target": 13317203838154184687, "profile": 15657897354478470176, "path": 5133371264879533603, "deps": [[3424551429995674438, "tracing_core", false, 9156574448665087020], [3722963349756955755, "once_cell", false, 3001542277175768057], [5986029879202738730, "log", false, 6744974836633744950]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-log-cff0d59ba0468ed0\\dep-lib-tracing_log", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}