{"rustc": 12488743700189009532, "features": "[\"alloc\", \"ansi\", \"default\", \"fmt\", \"nu-ansi-term\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 6355579909791343455, "path": 18211205697284639610, "deps": [[1017461770342116999, "sharded_slab", false, 1868929086407314516], [1359731229228270592, "thread_local", false, 17501768724496247169], [3424551429995674438, "tracing_core", false, 9156574448665087020], [3666196340704888985, "smallvec", false, 3450016790407122536], [8614575489689151157, "nu_ansi_term", false, 16538948949928459647], [10806489435541507125, "tracing_log", false, 17234677803309014042]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-5e1ce111465aecf1\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}