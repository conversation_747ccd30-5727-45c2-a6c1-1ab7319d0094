{"rustc": 12488743700189009532, "features": "[\"percent-encode\", \"percent-encoding\"]", "declared_features": "[\"aes-gcm\", \"base64\", \"hkdf\", \"hmac\", \"key-expansion\", \"percent-encode\", \"percent-encoding\", \"private\", \"rand\", \"secure\", \"sha2\", \"signed\", \"subtle\"]", "target": 678524939984925341, "profile": 15657897354478470176, "path": 13183057235690203601, "deps": [[40386456601120721, "percent_encoding", false, 348877878830084439], [2779053297469913730, "build_script_build", false, 785416377250736644], [12409575957772518135, "time", false, 2131033935219245394]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\cookie-6d913a44f75f759a\\dep-lib-cookie", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}