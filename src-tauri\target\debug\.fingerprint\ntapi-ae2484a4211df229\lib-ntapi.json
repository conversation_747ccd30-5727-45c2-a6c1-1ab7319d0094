{"rustc": 12488743700189009532, "features": "[\"default\", \"user\"]", "declared_features": "[\"default\", \"func-types\", \"impl-default\", \"kernel\", \"user\"]", "target": 13388410337598969226, "profile": 15657897354478470176, "path": 415354258204001157, "deps": [[10020888071089587331, "<PERSON>ap<PERSON>", false, 4985919973182291098], [11969445987175445311, "build_script_build", false, 2368136243632695867]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ntapi-ae2484a4211df229\\dep-lib-ntapi", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}