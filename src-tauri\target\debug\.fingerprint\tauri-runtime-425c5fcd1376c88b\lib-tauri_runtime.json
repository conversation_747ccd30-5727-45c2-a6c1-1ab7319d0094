{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 1725129574137805567, "deps": [[2671782512663819132, "tauri_utils", false, 501098159936826022], [3150220818285335163, "url", false, 6062674917897409888], [4143744114649553716, "raw_window_handle", false, 5439867478843892220], [6089812615193535349, "build_script_build", false, 9750412741508809269], [7606335748176206944, "dpi", false, 17045838260048947188], [9010263965687315507, "http", false, 3514203428925704760], [9689903380558560274, "serde", false, 12662985874143964422], [10806645703491011684, "thiserror", false, 14032229823463785456], [14585479307175734061, "windows", false, 1621046816081544285], [15367738274754116744, "serde_json", false, 14741482141302888685], [16727543399706004146, "cookie", false, 17007315089155735358]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-425c5fcd1376c88b\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}