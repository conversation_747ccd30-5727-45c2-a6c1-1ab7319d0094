{"rustc": 12488743700189009532, "features": "[\"default\"]", "declared_features": "[\"default\", \"implement\"]", "target": 5074702274061800001, "profile": 15657897354478470176, "path": 12503044087108065891, "deps": [[14322346790800707264, "windows_targets", false, 13662119225381416487]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\windows-core-7c6de8b7f920afa2\\dep-lib-windows_core", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}