{"rustc": 12488743700189009532, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"schemars_0_9\", \"schemars_1\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 5290030462671737236, "path": 15013052133811474219, "deps": [[7026957619838884710, "serde_with_macros", false, 9110618544839175807], [9689903380558560274, "serde", false, 12662985874143964422], [16257276029081467297, "serde_derive", false, 982129096129896074]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\serde_with-b3a79c71a9e8e96c\\dep-lib-serde_with", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}