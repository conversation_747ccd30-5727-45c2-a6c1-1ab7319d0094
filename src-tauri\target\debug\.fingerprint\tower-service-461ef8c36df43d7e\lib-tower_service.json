{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 4262671303997282168, "profile": 15657897354478470176, "path": 16076243925078825079, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-service-461ef8c36df43d7e\\dep-lib-tower_service", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}