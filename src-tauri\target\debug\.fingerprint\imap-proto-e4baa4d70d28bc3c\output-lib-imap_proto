{"$message_type": "future_incompat", "future_incompat_report": [{"diagnostic": {"$message_type": "diagnostic", "message": "trailing semicolon in macro used in expression position", "code": {"code": "semicolon_in_expressions_from_macros", "explanation": null}, "level": "warning", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 482, "byte_end": 483, "line_start": 15, "line_end": 15, "column_start": 43, "column_end": 44, "is_primary": true, "text": [{"text": "        parenthesized_list!($i, call!($f));", "highlight_start": 43, "highlight_end": 44}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 12053, "byte_end": 12076, "line_start": 376, "line_end": 376, "column_start": 13, "column_end": 36, "is_primary": false, "text": [{"text": "      match $submac!(i_, $($args)*) {", "highlight_start": 13, "highlight_end": 36}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 12172, "byte_end": 12203, "line_start": 380, "line_end": 380, "column_start": 11, "column_end": 42, "is_primary": false, "text": [{"text": "          do_parse!(__impl i_, $($rest)*)", "highlight_start": 11, "highlight_end": 42}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 12172, "byte_end": 12203, "line_start": 380, "line_end": 380, "column_start": 11, "column_end": 42, "is_primary": false, "text": [{"text": "          do_parse!(__impl i_, $($rest)*)", "highlight_start": 11, "highlight_end": 42}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 13896, "byte_end": 13927, "line_start": 450, "line_end": 450, "column_start": 7, "column_end": 38, "is_primary": false, "text": [{"text": "      do_parse!(__impl $i, $($rest)*)", "highlight_start": 7, "highlight_end": 38}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6700, "byte_end": 6724, "line_start": 190, "line_end": 190, "column_start": 17, "column_end": 41, "is_primary": false, "text": [{"text": "      let res = $subrule!(i_, $($args)*);", "highlight_start": 17, "highlight_end": 41}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 8289, "byte_end": 8323, "line_start": 252, "line_end": 252, "column_start": 7, "column_end": 41, "is_primary": false, "text": [{"text": "      alt!(__impl $i, $($rest)* | __end)", "highlight_start": 7, "highlight_end": 41}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 3504, "byte_end": 3526, "line_start": 124, "line_end": 124, "column_start": 13, "column_end": 35, "is_primary": false, "text": [{"text": "            $submac!(i, $($args)*)", "highlight_start": 13, "highlight_end": 35}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\body.rs", "byte_start": 294, "byte_end": 667, "line_start": 17, "line_end": 26, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "named!(pub section_msgtext<MessageSection>, alt!(", "highlight_start": 1, "highlight_end": 50}, {"text": "    do_parse!(", "highlight_start": 1, "highlight_end": 15}, {"text": "        tag_no_case!(\"HEADER.FIELDS\") >>", "highlight_start": 1, "highlight_end": 41}, {"text": "        opt!(tag_no_case!(\".NOT\")) >>", "highlight_start": 1, "highlight_end": 38}, {"text": "        tag!(\" \") >>", "highlight_start": 1, "highlight_end": 21}, {"text": "        parenthesized_list!(astring) >>", "highlight_start": 1, "highlight_end": 40}, {"text": "        (MessageSection::Header)) |", "highlight_start": 1, "highlight_end": 36}, {"text": "    do_parse!(tag_no_case!(\"HEADER\") >> (MessageSection::Header)) |", "highlight_start": 1, "highlight_end": 68}, {"text": "    do_parse!(tag_no_case!(\"TEXT\") >> (MessageSection::Text))", "highlight_start": 1, "highlight_end": 62}, {"text": "));", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}, "macro_decl_name": "named!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 2588, "byte_end": 2606, "line_start": 103, "line_end": 103, "column_start": 1, "column_end": 19, "is_primary": false, "text": [{"text": "macro_rules! named (", "highlight_start": 1, "highlight_end": 19}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "alt!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6057, "byte_end": 6073, "line_start": 166, "line_end": 166, "column_start": 1, "column_end": 17, "is_primary": false, "text": [{"text": "macro_rules! alt (", "highlight_start": 1, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "alt!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6057, "byte_end": 6073, "line_start": 166, "line_end": 166, "column_start": 1, "column_end": 17, "is_primary": false, "text": [{"text": "macro_rules! alt (", "highlight_start": 1, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "parenthesized_list!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 239, "byte_end": 493, "line_start": 10, "line_end": 17, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "macro_rules! parenthesized_list (", "highlight_start": 1, "highlight_end": 34}, {"text": "    ($i:expr, $submac:ident!( $($args:tt)* )) => ({", "highlight_start": 1, "highlight_end": 52}, {"text": "        paren_delimited!($i, separated_list!(char!(' '), $submac!($($args)*)))", "highlight_start": 1, "highlight_end": 79}, {"text": "    });", "highlight_start": 1, "highlight_end": 8}, {"text": "    ($i:expr, $f:expr) => (", "highlight_start": 1, "highlight_end": 28}, {"text": "        parenthesized_list!($i, call!($f));", "highlight_start": 1, "highlight_end": 44}, {"text": "    );", "highlight_start": 1, "highlight_end": 7}, {"text": ");", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "macro invocations at the end of a block are treated as expressions", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_list`", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: trailing semicolon in macro used in expression position\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs:15:43\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        parenthesized_list!($i, call!($f));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\body.rs:17:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0mnamed!(pub section_msgtext<MessageSection>, alt!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    do_parse!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        tag_no_case!(\"HEADER.FIELDS\") >>\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        opt!(tag_no_case!(\".NOT\")) >>\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    do_parse!(tag_no_case!(\"TEXT\") >> (MessageSection::Text))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|__-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: macro invocations at the end of a block are treated as expressions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_list`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the macro `parenthesized_list` which comes from the expansion of the macro `named` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "trailing semicolon in macro used in expression position", "code": {"code": "semicolon_in_expressions_from_macros", "explanation": null}, "level": "warning", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 765, "byte_end": 766, "line_start": 24, "line_end": 24, "column_start": 52, "column_end": 53, "is_primary": true, "text": [{"text": "        parenthesized_nonempty_list!($i, call!($f));", "highlight_start": 52, "highlight_end": 53}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 15974, "byte_end": 15996, "line_start": 503, "line_end": 503, "column_start": 44, "column_end": 66, "is_primary": false, "text": [{"text": "    $crate::combinator::mapc($i, move |i| {$submac!(i, $($args)*)}, $g)", "highlight_start": 44, "highlight_end": 66}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 16070, "byte_end": 16110, "line_start": 506, "line_end": 506, "column_start": 5, "column_end": 45, "is_primary": false, "text": [{"text": "    map!(__impl $i, $submac!($($args)*), $g);", "highlight_start": 5, "highlight_end": 45}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6700, "byte_end": 6724, "line_start": 190, "line_end": 190, "column_start": 17, "column_end": 41, "is_primary": false, "text": [{"text": "      let res = $subrule!(i_, $($args)*);", "highlight_start": 17, "highlight_end": 41}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6825, "byte_end": 6851, "line_start": 194, "line_end": 194, "column_start": 21, "column_end": 47, "is_primary": false, "text": [{"text": "          let out = alt!(__impl $i, $($rest)*);", "highlight_start": 21, "highlight_end": 47}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 8289, "byte_end": 8323, "line_start": 252, "line_end": 252, "column_start": 7, "column_end": 41, "is_primary": false, "text": [{"text": "      alt!(__impl $i, $($rest)* | __end)", "highlight_start": 7, "highlight_end": 41}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 3504, "byte_end": 3526, "line_start": 124, "line_end": 124, "column_start": 13, "column_end": 35, "is_primary": false, "text": [{"text": "            $submac!(i, $($args)*)", "highlight_start": 13, "highlight_end": 35}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\body_structure.rs", "byte_start": 3728, "byte_end": 3978, "line_start": 92, "line_end": 96, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "named!(body_lang<Option<Vec<&str>>>, alt!(", "highlight_start": 1, "highlight_end": 43}, {"text": "    // body language seems to refer to RFC 3066 language tags, which should be ASCII-only", "highlight_start": 1, "highlight_end": 90}, {"text": "    map!(nstring_utf8, |v| v.map(|s| vec![s])) |", "highlight_start": 1, "highlight_end": 49}, {"text": "    map!(parenthesized_nonempty_list!(string_utf8), Option::from)", "highlight_start": 1, "highlight_end": 66}, {"text": "));", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}, "macro_decl_name": "named!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 2588, "byte_end": 2606, "line_start": 103, "line_end": 103, "column_start": 1, "column_end": 19, "is_primary": false, "text": [{"text": "macro_rules! named (", "highlight_start": 1, "highlight_end": 19}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "alt!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6057, "byte_end": 6073, "line_start": 166, "line_end": 166, "column_start": 1, "column_end": 17, "is_primary": false, "text": [{"text": "macro_rules! alt (", "highlight_start": 1, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "alt!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6057, "byte_end": 6073, "line_start": 166, "line_end": 166, "column_start": 1, "column_end": 17, "is_primary": false, "text": [{"text": "macro_rules! alt (", "highlight_start": 1, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "alt!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6057, "byte_end": 6073, "line_start": 166, "line_end": 166, "column_start": 1, "column_end": 17, "is_primary": false, "text": [{"text": "macro_rules! alt (", "highlight_start": 1, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "map!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 15806, "byte_end": 15822, "line_start": 500, "line_end": 500, "column_start": 1, "column_end": 17, "is_primary": false, "text": [{"text": "macro_rules! map(", "highlight_start": 1, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "map!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 15806, "byte_end": 15822, "line_start": 500, "line_end": 500, "column_start": 1, "column_end": 17, "is_primary": false, "text": [{"text": "macro_rules! map(", "highlight_start": 1, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "parenthesized_nonempty_list!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 495, "byte_end": 776, "line_start": 19, "line_end": 26, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "macro_rules! parenthesized_nonempty_list (", "highlight_start": 1, "highlight_end": 43}, {"text": "    ($i:expr, $submac:ident!( $($args:tt)* )) => ({", "highlight_start": 1, "highlight_end": 52}, {"text": "        paren_delimited!($i, separated_nonempty_list!(char!(' '), $submac!($($args)*)))", "highlight_start": 1, "highlight_end": 88}, {"text": "    });", "highlight_start": 1, "highlight_end": 8}, {"text": "    ($i:expr, $f:expr) => (", "highlight_start": 1, "highlight_end": 28}, {"text": "        parenthesized_nonempty_list!($i, call!($f));", "highlight_start": 1, "highlight_end": 53}, {"text": "    );", "highlight_start": 1, "highlight_end": 7}, {"text": ");", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "macro invocations at the end of a block are treated as expressions", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_nonempty_list`", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: trailing semicolon in macro used in expression position\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs:24:52\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        parenthesized_nonempty_list!($i, call!($f));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\body_structure.rs:92:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m92\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0mnamed!(body_lang<Option<Vec<&str>>>, alt!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m93\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    // body language seems to refer to RFC 3066 language tags, which should be ASCII-only\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m94\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    map!(nstring_utf8, |v| v.map(|s| vec![s])) |\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m95\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    map!(parenthesized_nonempty_list!(string_utf8), Option::from)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m96\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|__-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: macro invocations at the end of a block are treated as expressions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_nonempty_list`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the macro `parenthesized_nonempty_list` which comes from the expansion of the macro `named` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "trailing semicolon in macro used in expression position", "code": {"code": "semicolon_in_expressions_from_macros", "explanation": null}, "level": "warning", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 765, "byte_end": 766, "line_start": 24, "line_end": 24, "column_start": 52, "column_end": 53, "is_primary": true, "text": [{"text": "        parenthesized_nonempty_list!($i, call!($f));", "highlight_start": 52, "highlight_end": 53}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 15974, "byte_end": 15996, "line_start": 503, "line_end": 503, "column_start": 44, "column_end": 66, "is_primary": false, "text": [{"text": "    $crate::combinator::mapc($i, move |i| {$submac!(i, $($args)*)}, $g)", "highlight_start": 44, "highlight_end": 66}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 16070, "byte_end": 16110, "line_start": 506, "line_end": 506, "column_start": 5, "column_end": 45, "is_primary": false, "text": [{"text": "    map!(__impl $i, $submac!($($args)*), $g);", "highlight_start": 5, "highlight_end": 45}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6700, "byte_end": 6724, "line_start": 190, "line_end": 190, "column_start": 17, "column_end": 41, "is_primary": false, "text": [{"text": "      let res = $subrule!(i_, $($args)*);", "highlight_start": 17, "highlight_end": 41}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6825, "byte_end": 6851, "line_start": 194, "line_end": 194, "column_start": 21, "column_end": 47, "is_primary": false, "text": [{"text": "          let out = alt!(__impl $i, $($rest)*);", "highlight_start": 21, "highlight_end": 47}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6825, "byte_end": 6851, "line_start": 194, "line_end": 194, "column_start": 21, "column_end": 47, "is_primary": false, "text": [{"text": "          let out = alt!(__impl $i, $($rest)*);", "highlight_start": 21, "highlight_end": 47}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 8289, "byte_end": 8323, "line_start": 252, "line_end": 252, "column_start": 7, "column_end": 41, "is_primary": false, "text": [{"text": "      alt!(__impl $i, $($rest)* | __end)", "highlight_start": 7, "highlight_end": 41}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 3504, "byte_end": 3526, "line_start": 124, "line_end": 124, "column_start": 13, "column_end": 35, "is_primary": false, "text": [{"text": "            $submac!(i, $($args)*)", "highlight_start": 13, "highlight_end": 35}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\body_structure.rs", "byte_start": 4218, "byte_end": 4616, "line_start": 108, "line_end": 114, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "named!(body_extension<BodyExtension>, alt!(", "highlight_start": 1, "highlight_end": 44}, {"text": "    map!(number, |n| BodyExtension::Num(n)) |", "highlight_start": 1, "highlight_end": 46}, {"text": "    // Cannot find documentation on character encoding for body extension values.", "highlight_start": 1, "highlight_end": 82}, {"text": "    // So far, assuming UTF-8 seems fine, please report if you run into issues here.", "highlight_start": 1, "highlight_end": 85}, {"text": "    map!(nstring_utf8, |s| BodyExtension::Str(s)) |", "highlight_start": 1, "highlight_end": 52}, {"text": "    map!(parenthesized_nonempty_list!(body_extension), |ext| BodyExtension::List(ext))", "highlight_start": 1, "highlight_end": 87}, {"text": "));", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}, "macro_decl_name": "named!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 2588, "byte_end": 2606, "line_start": 103, "line_end": 103, "column_start": 1, "column_end": 19, "is_primary": false, "text": [{"text": "macro_rules! named (", "highlight_start": 1, "highlight_end": 19}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "alt!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6057, "byte_end": 6073, "line_start": 166, "line_end": 166, "column_start": 1, "column_end": 17, "is_primary": false, "text": [{"text": "macro_rules! alt (", "highlight_start": 1, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "alt!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6057, "byte_end": 6073, "line_start": 166, "line_end": 166, "column_start": 1, "column_end": 17, "is_primary": false, "text": [{"text": "macro_rules! alt (", "highlight_start": 1, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "alt!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6057, "byte_end": 6073, "line_start": 166, "line_end": 166, "column_start": 1, "column_end": 17, "is_primary": false, "text": [{"text": "macro_rules! alt (", "highlight_start": 1, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "alt!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\branch\\macros.rs", "byte_start": 6057, "byte_end": 6073, "line_start": 166, "line_end": 166, "column_start": 1, "column_end": 17, "is_primary": false, "text": [{"text": "macro_rules! alt (", "highlight_start": 1, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "map!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 15806, "byte_end": 15822, "line_start": 500, "line_end": 500, "column_start": 1, "column_end": 17, "is_primary": false, "text": [{"text": "macro_rules! map(", "highlight_start": 1, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "map!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 15806, "byte_end": 15822, "line_start": 500, "line_end": 500, "column_start": 1, "column_end": 17, "is_primary": false, "text": [{"text": "macro_rules! map(", "highlight_start": 1, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "parenthesized_nonempty_list!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 495, "byte_end": 776, "line_start": 19, "line_end": 26, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "macro_rules! parenthesized_nonempty_list (", "highlight_start": 1, "highlight_end": 43}, {"text": "    ($i:expr, $submac:ident!( $($args:tt)* )) => ({", "highlight_start": 1, "highlight_end": 52}, {"text": "        paren_delimited!($i, separated_nonempty_list!(char!(' '), $submac!($($args)*)))", "highlight_start": 1, "highlight_end": 88}, {"text": "    });", "highlight_start": 1, "highlight_end": 8}, {"text": "    ($i:expr, $f:expr) => (", "highlight_start": 1, "highlight_end": 28}, {"text": "        parenthesized_nonempty_list!($i, call!($f));", "highlight_start": 1, "highlight_end": 53}, {"text": "    );", "highlight_start": 1, "highlight_end": 7}, {"text": ");", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "macro invocations at the end of a block are treated as expressions", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_nonempty_list`", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: trailing semicolon in macro used in expression position\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs:24:52\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        parenthesized_nonempty_list!($i, call!($f));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\body_structure.rs:108:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0mnamed!(body_extension<BodyExtension>, alt!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    map!(number, |n| BodyExtension::Num(n)) |\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m113\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    map!(parenthesized_nonempty_list!(body_extension), |ext| BodyExtension::List(ext))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m114\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|__-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: macro invocations at the end of a block are treated as expressions\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_nonempty_list`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the macro `parenthesized_nonempty_list` which comes from the expansion of the macro `named` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "trailing semicolon in macro used in expression position", "code": {"code": "semicolon_in_expressions_from_macros", "explanation": null}, "level": "warning", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 482, "byte_end": 483, "line_start": 15, "line_end": 15, "column_start": 43, "column_end": 44, "is_primary": true, "text": [{"text": "        parenthesized_list!($i, call!($f));", "highlight_start": 43, "highlight_end": 44}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 3504, "byte_end": 3526, "line_start": 124, "line_end": 124, "column_start": 13, "column_end": 35, "is_primary": false, "text": [{"text": "            $submac!(i, $($args)*)", "highlight_start": 13, "highlight_end": 35}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\parser\\rfc3501.rs", "byte_start": 1316, "byte_end": 1371, "line_start": 68, "line_end": 68, "column_start": 1, "column_end": 56, "is_primary": false, "text": [{"text": "named!(flag_list<Vec<&str>>, parenthesized_list!(flag));", "highlight_start": 1, "highlight_end": 56}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}, "macro_decl_name": "named!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 2588, "byte_end": 2606, "line_start": 103, "line_end": 103, "column_start": 1, "column_end": 19, "is_primary": false, "text": [{"text": "macro_rules! named (", "highlight_start": 1, "highlight_end": 19}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "parenthesized_list!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 239, "byte_end": 493, "line_start": 10, "line_end": 17, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "macro_rules! parenthesized_list (", "highlight_start": 1, "highlight_end": 34}, {"text": "    ($i:expr, $submac:ident!( $($args:tt)* )) => ({", "highlight_start": 1, "highlight_end": 52}, {"text": "        paren_delimited!($i, separated_list!(char!(' '), $submac!($($args)*)))", "highlight_start": 1, "highlight_end": 79}, {"text": "    });", "highlight_start": 1, "highlight_end": 8}, {"text": "    ($i:expr, $f:expr) => (", "highlight_start": 1, "highlight_end": 28}, {"text": "        parenthesized_list!($i, call!($f));", "highlight_start": 1, "highlight_end": 44}, {"text": "    );", "highlight_start": 1, "highlight_end": 7}, {"text": ");", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "macro invocations at the end of a block are treated as expressions", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_list`", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: trailing semicolon in macro used in expression position\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs:15:43\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        parenthesized_list!($i, call!($f));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\parser\\rfc3501.rs:68:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m68\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mnamed!(flag_list<Vec<&str>>, parenthesized_list!(flag));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: macro invocations at the end of a block are treated as expressions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_list`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the macro `parenthesized_list` which comes from the expansion of the macro `named` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "trailing semicolon in macro used in expression position", "code": {"code": "semicolon_in_expressions_from_macros", "explanation": null}, "level": "warning", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 765, "byte_end": 766, "line_start": 24, "line_end": 24, "column_start": 52, "column_end": 53, "is_primary": true, "text": [{"text": "        parenthesized_nonempty_list!($i, call!($f));", "highlight_start": 52, "highlight_end": 53}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 12545, "byte_end": 12568, "line_start": 395, "line_end": 395, "column_start": 14, "column_end": 37, "is_primary": false, "text": [{"text": "      match  $submac!(i_, $($args)*) {", "highlight_start": 14, "highlight_end": 37}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 12172, "byte_end": 12203, "line_start": 380, "line_end": 380, "column_start": 11, "column_end": 42, "is_primary": false, "text": [{"text": "          do_parse!(__impl i_, $($rest)*)", "highlight_start": 11, "highlight_end": 42}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 13896, "byte_end": 13927, "line_start": 450, "line_end": 450, "column_start": 7, "column_end": 38, "is_primary": false, "text": [{"text": "      do_parse!(__impl $i, $($rest)*)", "highlight_start": 7, "highlight_end": 38}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 23870, "byte_end": 23892, "line_start": 725, "line_end": 725, "column_start": 40, "column_end": 62, "is_primary": false, "text": [{"text": "      $crate::combinator::optc($i, |i| $submac!(i, $($args)*))", "highlight_start": 40, "highlight_end": 62}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 12545, "byte_end": 12568, "line_start": 395, "line_end": 395, "column_start": 14, "column_end": 37, "is_primary": false, "text": [{"text": "      match  $submac!(i_, $($args)*) {", "highlight_start": 14, "highlight_end": 37}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 12172, "byte_end": 12203, "line_start": 380, "line_end": 380, "column_start": 11, "column_end": 42, "is_primary": false, "text": [{"text": "          do_parse!(__impl i_, $($rest)*)", "highlight_start": 11, "highlight_end": 42}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 13896, "byte_end": 13927, "line_start": 450, "line_end": 450, "column_start": 7, "column_end": 38, "is_primary": false, "text": [{"text": "      do_parse!(__impl $i, $($rest)*)", "highlight_start": 7, "highlight_end": 38}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 3504, "byte_end": 3526, "line_start": 124, "line_end": 124, "column_start": 13, "column_end": 35, "is_primary": false, "text": [{"text": "            $submac!(i, $($args)*)", "highlight_start": 13, "highlight_end": 35}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\parser\\rfc3501.rs", "byte_start": 1576, "byte_end": 1844, "line_start": 80, "line_end": 88, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "named!(resp_text_code_badcharset<ResponseCode>, do_parse!(", "highlight_start": 1, "highlight_end": 59}, {"text": "    tag_no_case!(\"BADCHARSET\") >>", "highlight_start": 1, "highlight_end": 34}, {"text": "    ch: opt!(do_parse!(", "highlight_start": 1, "highlight_end": 24}, {"text": "        tag!(\" \") >>", "highlight_start": 1, "highlight_end": 21}, {"text": "        charsets: parenthesized_nonempty_list!(astring_utf8) >>", "highlight_start": 1, "highlight_end": 64}, {"text": "        (charsets)", "highlight_start": 1, "highlight_end": 19}, {"text": "    )) >>", "highlight_start": 1, "highlight_end": 10}, {"text": "    (ResponseCode::Bad<PERSON><PERSON><PERSON>(ch))", "highlight_start": 1, "highlight_end": 35}, {"text": "));", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}, "macro_decl_name": "named!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 2588, "byte_end": 2606, "line_start": 103, "line_end": 103, "column_start": 1, "column_end": 19, "is_primary": false, "text": [{"text": "macro_rules! named (", "highlight_start": 1, "highlight_end": 19}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "opt!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 23758, "byte_end": 23774, "line_start": 722, "line_end": 722, "column_start": 1, "column_end": 17, "is_primary": false, "text": [{"text": "macro_rules! opt(", "highlight_start": 1, "highlight_end": 17}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "parenthesized_nonempty_list!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 495, "byte_end": 776, "line_start": 19, "line_end": 26, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "macro_rules! parenthesized_nonempty_list (", "highlight_start": 1, "highlight_end": 43}, {"text": "    ($i:expr, $submac:ident!( $($args:tt)* )) => ({", "highlight_start": 1, "highlight_end": 52}, {"text": "        paren_delimited!($i, separated_nonempty_list!(char!(' '), $submac!($($args)*)))", "highlight_start": 1, "highlight_end": 88}, {"text": "    });", "highlight_start": 1, "highlight_end": 8}, {"text": "    ($i:expr, $f:expr) => (", "highlight_start": 1, "highlight_end": 28}, {"text": "        parenthesized_nonempty_list!($i, call!($f));", "highlight_start": 1, "highlight_end": 53}, {"text": "    );", "highlight_start": 1, "highlight_end": 7}, {"text": ");", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "macro invocations at the end of a block are treated as expressions", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_nonempty_list`", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: trailing semicolon in macro used in expression position\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs:24:52\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        parenthesized_nonempty_list!($i, call!($f));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\parser\\rfc3501.rs:80:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m80\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0mnamed!(resp_text_code_badcharset<ResponseCode>, do_parse!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m81\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    tag_no_case!(\"BADCHARSET\") >>\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m82\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ch: opt!(do_parse!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m83\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        tag!(\" \") >>\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m87\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    (ResponseCode::BadCharset(ch))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m88\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|__-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: macro invocations at the end of a block are treated as expressions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_nonempty_list`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the macro `parenthesized_nonempty_list` which comes from the expansion of the macro `named` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "trailing semicolon in macro used in expression position", "code": {"code": "semicolon_in_expressions_from_macros", "explanation": null}, "level": "warning", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 482, "byte_end": 483, "line_start": 15, "line_end": 15, "column_start": 43, "column_end": 44, "is_primary": true, "text": [{"text": "        parenthesized_list!($i, call!($f));", "highlight_start": 43, "highlight_end": 44}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 12545, "byte_end": 12568, "line_start": 395, "line_end": 395, "column_start": 14, "column_end": 37, "is_primary": false, "text": [{"text": "      match  $submac!(i_, $($args)*) {", "highlight_start": 14, "highlight_end": 37}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 12172, "byte_end": 12203, "line_start": 380, "line_end": 380, "column_start": 11, "column_end": 42, "is_primary": false, "text": [{"text": "          do_parse!(__impl i_, $($rest)*)", "highlight_start": 11, "highlight_end": 42}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 13896, "byte_end": 13927, "line_start": 450, "line_end": 450, "column_start": 7, "column_end": 38, "is_primary": false, "text": [{"text": "      do_parse!(__impl $i, $($rest)*)", "highlight_start": 7, "highlight_end": 38}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 3504, "byte_end": 3526, "line_start": 124, "line_end": 124, "column_start": 13, "column_end": 35, "is_primary": false, "text": [{"text": "            $submac!(i, $($args)*)", "highlight_start": 13, "highlight_end": 35}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\parser\\rfc3501.rs", "byte_start": 2079, "byte_end": 2271, "line_start": 100, "line_end": 104, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "named!(resp_text_code_permanent_flags<ResponseCode>, do_parse!(", "highlight_start": 1, "highlight_end": 64}, {"text": "    tag_no_case!(\"PERMANENTFLAGS \") >>", "highlight_start": 1, "highlight_end": 39}, {"text": "    flags: parenthesized_list!(flag_perm) >>", "highlight_start": 1, "highlight_end": 45}, {"text": "    (ResponseCode::PermanentFlags(flags))", "highlight_start": 1, "highlight_end": 42}, {"text": "));", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}, "macro_decl_name": "named!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 2588, "byte_end": 2606, "line_start": 103, "line_end": 103, "column_start": 1, "column_end": 19, "is_primary": false, "text": [{"text": "macro_rules! named (", "highlight_start": 1, "highlight_end": 19}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "parenthesized_list!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 239, "byte_end": 493, "line_start": 10, "line_end": 17, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "macro_rules! parenthesized_list (", "highlight_start": 1, "highlight_end": 34}, {"text": "    ($i:expr, $submac:ident!( $($args:tt)* )) => ({", "highlight_start": 1, "highlight_end": 52}, {"text": "        paren_delimited!($i, separated_list!(char!(' '), $submac!($($args)*)))", "highlight_start": 1, "highlight_end": 79}, {"text": "    });", "highlight_start": 1, "highlight_end": 8}, {"text": "    ($i:expr, $f:expr) => (", "highlight_start": 1, "highlight_end": 28}, {"text": "        parenthesized_list!($i, call!($f));", "highlight_start": 1, "highlight_end": 44}, {"text": "    );", "highlight_start": 1, "highlight_end": 7}, {"text": ");", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "macro invocations at the end of a block are treated as expressions", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_list`", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: trailing semicolon in macro used in expression position\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs:15:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        parenthesized_list!($i, call!($f));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\parser\\rfc3501.rs:100:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0mnamed!(resp_text_code_permanent_flags<ResponseCode>, do_parse!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m101\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    tag_no_case!(\"PERMANENTFLAGS \") >>\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m102\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    flags: parenthesized_list!(flag_perm) >>\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m103\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    (ResponseCode::PermanentFlags(flags))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|__-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: macro invocations at the end of a block are treated as expressions\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_list`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the macro `parenthesized_list` which comes from the expansion of the macro `named` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "trailing semicolon in macro used in expression position", "code": {"code": "semicolon_in_expressions_from_macros", "explanation": null}, "level": "warning", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 765, "byte_end": 766, "line_start": 24, "line_end": 24, "column_start": 52, "column_end": 53, "is_primary": true, "text": [{"text": "        parenthesized_nonempty_list!($i, call!($f));", "highlight_start": 52, "highlight_end": 53}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 3504, "byte_end": 3526, "line_start": 124, "line_end": 124, "column_start": 13, "column_end": 35, "is_primary": false, "text": [{"text": "            $submac!(i, $($args)*)", "highlight_start": 13, "highlight_end": 35}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\parser\\rfc3501.rs", "byte_start": 6698, "byte_end": 6785, "line_start": 274, "line_end": 274, "column_start": 1, "column_end": 88, "is_primary": false, "text": [{"text": "named!(status_att_list<Vec<StatusAttribute>>, parenthesized_nonempty_list!(status_att));", "highlight_start": 1, "highlight_end": 88}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}, "macro_decl_name": "named!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 2588, "byte_end": 2606, "line_start": 103, "line_end": 103, "column_start": 1, "column_end": 19, "is_primary": false, "text": [{"text": "macro_rules! named (", "highlight_start": 1, "highlight_end": 19}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "parenthesized_nonempty_list!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 495, "byte_end": 776, "line_start": 19, "line_end": 26, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "macro_rules! parenthesized_nonempty_list (", "highlight_start": 1, "highlight_end": 43}, {"text": "    ($i:expr, $submac:ident!( $($args:tt)* )) => ({", "highlight_start": 1, "highlight_end": 52}, {"text": "        paren_delimited!($i, separated_nonempty_list!(char!(' '), $submac!($($args)*)))", "highlight_start": 1, "highlight_end": 88}, {"text": "    });", "highlight_start": 1, "highlight_end": 8}, {"text": "    ($i:expr, $f:expr) => (", "highlight_start": 1, "highlight_end": 28}, {"text": "        parenthesized_nonempty_list!($i, call!($f));", "highlight_start": 1, "highlight_end": 53}, {"text": "    );", "highlight_start": 1, "highlight_end": 7}, {"text": ");", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "macro invocations at the end of a block are treated as expressions", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_nonempty_list`", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: trailing semicolon in macro used in expression position\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs:24:52\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        parenthesized_nonempty_list!($i, call!($f));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\parser\\rfc3501.rs:274:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mnamed!(status_att_list<Vec<StatusAttribute>>, parenthesized_nonempty_list!(status_att));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: macro invocations at the end of a block are treated as expressions\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_nonempty_list`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the macro `parenthesized_nonempty_list` which comes from the expansion of the macro `named` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "trailing semicolon in macro used in expression position", "code": {"code": "semicolon_in_expressions_from_macros", "explanation": null}, "level": "warning", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 765, "byte_end": 766, "line_start": 24, "line_end": 24, "column_start": 52, "column_end": 53, "is_primary": true, "text": [{"text": "        parenthesized_nonempty_list!($i, call!($f));", "highlight_start": 52, "highlight_end": 53}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 3504, "byte_end": 3526, "line_start": 124, "line_end": 124, "column_start": 13, "column_end": 35, "is_primary": false, "text": [{"text": "            $submac!(i, $($args)*)", "highlight_start": 13, "highlight_end": 35}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\parser\\rfc3501.rs", "byte_start": 10493, "byte_end": 10573, "line_start": 433, "line_end": 433, "column_start": 1, "column_end": 81, "is_primary": false, "text": [{"text": "named!(msg_att_list<Vec<AttributeValue>>, parenthesized_nonempty_list!(msg_att));", "highlight_start": 1, "highlight_end": 81}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}, "macro_decl_name": "named!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 2588, "byte_end": 2606, "line_start": 103, "line_end": 103, "column_start": 1, "column_end": 19, "is_primary": false, "text": [{"text": "macro_rules! named (", "highlight_start": 1, "highlight_end": 19}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "parenthesized_nonempty_list!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 495, "byte_end": 776, "line_start": 19, "line_end": 26, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "macro_rules! parenthesized_nonempty_list (", "highlight_start": 1, "highlight_end": 43}, {"text": "    ($i:expr, $submac:ident!( $($args:tt)* )) => ({", "highlight_start": 1, "highlight_end": 52}, {"text": "        paren_delimited!($i, separated_nonempty_list!(char!(' '), $submac!($($args)*)))", "highlight_start": 1, "highlight_end": 88}, {"text": "    });", "highlight_start": 1, "highlight_end": 8}, {"text": "    ($i:expr, $f:expr) => (", "highlight_start": 1, "highlight_end": 28}, {"text": "        parenthesized_nonempty_list!($i, call!($f));", "highlight_start": 1, "highlight_end": 53}, {"text": "    );", "highlight_start": 1, "highlight_end": 7}, {"text": ");", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "macro invocations at the end of a block are treated as expressions", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_nonempty_list`", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: trailing semicolon in macro used in expression position\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs:24:52\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        parenthesized_nonempty_list!($i, call!($f));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\parser\\rfc3501.rs:433:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m433\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mnamed!(msg_att_list<Vec<AttributeValue>>, parenthesized_nonempty_list!(msg_att));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: macro invocations at the end of a block are treated as expressions\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: to ignore the value produced by the macro, add a semicolon after the invocation of `parenthesized_nonempty_list`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the macro `parenthesized_nonempty_list` which comes from the expansion of the macro `named` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "trailing semicolon in macro used in expression position", "code": {"code": "semicolon_in_expressions_from_macros", "explanation": null}, "level": "warning", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 226, "byte_end": 227, "line_start": 6, "line_end": 6, "column_start": 40, "column_end": 41, "is_primary": true, "text": [{"text": "        paren_delimited!($i, call!($f));", "highlight_start": 40, "highlight_end": 41}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 12545, "byte_end": 12568, "line_start": 395, "line_end": 395, "column_start": 14, "column_end": 37, "is_primary": false, "text": [{"text": "      match  $submac!(i_, $($args)*) {", "highlight_start": 14, "highlight_end": 37}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 12172, "byte_end": 12203, "line_start": 380, "line_end": 380, "column_start": 11, "column_end": 42, "is_primary": false, "text": [{"text": "          do_parse!(__impl i_, $($rest)*)", "highlight_start": 11, "highlight_end": 42}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 13896, "byte_end": 13927, "line_start": 450, "line_end": 450, "column_start": 7, "column_end": 38, "is_primary": false, "text": [{"text": "      do_parse!(__impl $i, $($rest)*)", "highlight_start": 7, "highlight_end": 38}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 3504, "byte_end": 3526, "line_start": 124, "line_end": 124, "column_start": 13, "column_end": 35, "is_primary": false, "text": [{"text": "            $submac!(i, $($args)*)", "highlight_start": 13, "highlight_end": 35}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": {"span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\parser\\rfc4551.rs", "byte_start": 1357, "byte_end": 1527, "line_start": 34, "line_end": 38, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "named!(pub (crate) msg_att_mod_seq<AttributeValue>, do_parse!(", "highlight_start": 1, "highlight_end": 63}, {"text": "    tag_no_case!(\"MODSEQ \") >>", "highlight_start": 1, "highlight_end": 31}, {"text": "    num: paren_delimited!(number_64) >>", "highlight_start": 1, "highlight_end": 40}, {"text": "    (AttributeValue::ModSeq(num))", "highlight_start": 1, "highlight_end": 34}, {"text": "));", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}, "macro_decl_name": "named!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\combinator\\macros.rs", "byte_start": 2588, "byte_end": 2606, "line_start": 103, "line_end": 103, "column_start": 1, "column_end": 19, "is_primary": false, "text": [{"text": "macro_rules! named (", "highlight_start": 1, "highlight_end": 19}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "do_parse!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\nom-5.1.3\\src\\sequence\\macros.rs", "byte_start": 10664, "byte_end": 10685, "line_start": 334, "line_end": 334, "column_start": 1, "column_end": 22, "is_primary": false, "text": [{"text": "macro_rules! do_parse (", "highlight_start": 1, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}, "macro_decl_name": "paren_delimited!", "def_site_span": {"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs", "byte_start": 0, "byte_end": 237, "line_start": 1, "line_end": 8, "column_start": 1, "column_end": 3, "is_primary": false, "text": [{"text": "macro_rules! paren_delimited (", "highlight_start": 1, "highlight_end": 31}, {"text": "    ($i:expr, $submac:ident!( $($args:tt)* )) => ({", "highlight_start": 1, "highlight_end": 52}, {"text": "        delimited!($i, char!('('), $submac!($($args)*), char!(')'))", "highlight_start": 1, "highlight_end": 68}, {"text": "    });", "highlight_start": 1, "highlight_end": 8}, {"text": "    ($i:expr, $f:expr) => (", "highlight_start": 1, "highlight_end": 28}, {"text": "        paren_delimited!($i, call!($f));", "highlight_start": 1, "highlight_end": 41}, {"text": "    );", "highlight_start": 1, "highlight_end": 7}, {"text": ");", "highlight_start": 1, "highlight_end": 3}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}}}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "macro invocations at the end of a block are treated as expressions", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "to ignore the value produced by the macro, add a semicolon after the invocation of `paren_delimited`", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: trailing semicolon in macro used in expression position\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\macros.rs:6:40\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        paren_delimited!($i, call!($f));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\imap-proto-0.10.2\\src\\parser\\rfc4551.rs:34:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0mnamed!(pub (crate) msg_att_mod_seq<AttributeValue>, do_parse!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    tag_no_case!(\"MODSEQ \") >>\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    num: paren_delimited!(number_64) >>\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m37\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    (AttributeValue::ModSeq(num))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|__-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #79813 <https://github.com/rust-lang/rust/issues/79813>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: macro invocations at the end of a block are treated as expressions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: to ignore the value produced by the macro, add a semicolon after the invocation of `paren_delimited`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the macro `paren_delimited` which comes from the expansion of the macro `named` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}}]}