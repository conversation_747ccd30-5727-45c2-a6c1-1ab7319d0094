{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 386963995487357571, "profile": 15657897354478470176, "path": 18232431237219519147, "deps": [[11060647489427778847, "winapi_util", false, 10730458443844670160]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\termcolor-c6d0f34dcfcc4c8d\\dep-lib-termcolor", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}