{"rustc": 12488743700189009532, "features": "[\"arrays\", \"legacy\", \"std\", \"zdict_builder\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"legacy\", \"no_asm\", \"pkg-config\", \"std\", \"thin\", \"zdict_builder\", \"zstdmt\"]", "target": 14413232512288093687, "profile": 15657897354478470176, "path": 10988627878085649249, "deps": [[4684437522915235464, "libc", false, 8772519399186178916], [8373447648276846408, "zstd_sys", false, 15558514459645733836], [11328745298441753262, "build_script_build", false, 8073289960099797295]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zstd-safe-3ba49b6c49167370\\dep-lib-zstd_safe", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}