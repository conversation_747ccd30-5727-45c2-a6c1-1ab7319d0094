cargo:rerun-if-env-changed=ZSTD_SYS_USE_PKG_CONFIG
OUT_DIR = Some(E:\UPanBeiFen\YAugment\YAugment\src-tauri\target\debug\build\zstd-sys-a4bc1e3d64933f59\out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(E:\UPanBeiFen\YAugment\YAugment\src-tauri\target\debug\deps;E:\UPanBeiFen\YAugment\YAugment\src-tauri\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\nodejs\;C:\Program Files\Git\cmd;C:\Program Files (x86)\ZeroTier\One\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files (x86)\WiX Toolset v3.11\bin;C:\Program Files\upx-5.0.0-win64;;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files (x86)\NetSarang\Xftp 8\;D:\TengX\WeiXinKaiFaXiaoChengXu\微信web开发者工具\dll;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files (x86)\WiX Toolset v3.11\bin;C:\Program Files\upx-5.0.0-win64;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Fiddler)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,crt-static,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some(-Ctarget-feature=+crt-static-Clink-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF)
OUT_DIR = Some(E:\UPanBeiFen\YAugment\YAugment\src-tauri\target\debug\build\zstd-sys-a4bc1e3d64933f59\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,crt-static,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
OUT_DIR = Some(E:\UPanBeiFen\YAugment\YAugment\src-tauri\target\debug\build\zstd-sys-a4bc1e3d64933f59\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,crt-static,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
OUT_DIR = Some(E:\UPanBeiFen\YAugment\YAugment\src-tauri\target\debug\build\zstd-sys-a4bc1e3d64933f59\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,crt-static,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
debug.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
entropy_common.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
error_private.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
fse_decompress.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
pool.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
threading.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_common.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
fse_compress.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
hist.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
huf_compress.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
zstd_compress.c
exit code: cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_compress_literals.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_compress_sequences.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_compress_superblock.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_double_fast.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_fast.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_lazy.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_ldm.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_opt.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_preSplit.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
zstdmt_compress.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
huf_decompress.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_ddict.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_decompress.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_decompress_block.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cover.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
divsufsort.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
fastcover.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zdict.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
zstd_v01.c
exit code: cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
zstd_v03.c
zstd_v02.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_v04.c
zstd_v05.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
zstd_v06.c
zstd_v07.c
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-fvisibility=hidden'
                   
0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\atlmfc\lib\x64
cargo:rustc-link-lib=static=zstd
cargo:rustc-link-search=native=E:\UPanBeiFen\YAugment\YAugment\src-tauri\target\debug\build\zstd-sys-a4bc1e3d64933f59\out
cargo:root=E:\UPanBeiFen\YAugment\YAugment\src-tauri\target\debug\build\zstd-sys-a4bc1e3d64933f59\out
cargo:include=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\zstd-sys-2.0.15+zstd.1.5.7\zstd/lib
