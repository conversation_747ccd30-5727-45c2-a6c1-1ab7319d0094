{"rustc": 12488743700189009532, "features": "[\"__tls\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"stream\", \"tokio-native-tls\", \"tokio-util\", \"wasm-streams\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 15657897354478470176, "path": 17251503022337877276, "deps": [[40386456601120721, "percent_encoding", false, 348877878830084439], [95042085696191081, "ipnet", false, 17308171020826104395], [264090853244900308, "sync_wrapper", false, 12380478480099139718], [784494742817713399, "tower_service", false, 10293051206577528705], [1288403060204016458, "tokio_util", false, 6696711646665129082], [1906322745568073236, "pin_project_lite", false, 377786499476956345], [2779053297469913730, "cookie_crate", false, 16841455798196774950], [3150220818285335163, "url", false, 6062674917897409888], [3722963349756955755, "once_cell", false, 3001542277175768057], [4405182208873388884, "http", false, 18286749706990962492], [5986029879202738730, "log", false, 6744974836633744950], [7414427314941361239, "hyper", false, 18421623742838647698], [7620660491849607393, "futures_core", false, 4949676933428321037], [8405603588346937335, "winreg", false, 912871141967068152], [8915503303801890683, "http_body", false, 15940265720583617557], [9689903380558560274, "serde", false, 12662985874143964422], [10229185211513642314, "mime", false, 7552261498337461961], [10629569228670356391, "futures_util", false, 18681567327667426], [12186126227181294540, "tokio_native_tls", false, 7712626001663509111], [12367227501898450486, "hyper_tls", false, 16876258743091623219], [12393800526703971956, "tokio", false, 13798810526153862019], [13809605890706463735, "h2", false, 4021519316968728553], [14564311161534545801, "encoding_rs", false, 10546360867697300839], [15367738274754116744, "serde_json", false, 14741482141302888685], [16066129441945555748, "bytes", false, 14972860757458678711], [16311359161338405624, "rustls_pemfile", false, 344493724907634049], [16542808166767769916, "serde_urlencoded", false, 2689829716544866071], [16785601910559813697, "native_tls_crate", false, 10286770631657771184], [17973378407174338648, "cookie_store", false, 3885521064815994395], [18066890886671768183, "base64", false, 13152560994917999454]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-48611e2913352308\\dep-lib-reqwest", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}