{"rustc": 12488743700189009532, "features": "[\"std\"]", "declared_features": "[\"std\"]", "target": 13660428293521089546, "profile": 15657897354478470176, "path": 3707690582469910780, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\error-code-579f36acacc2e764\\dep-lib-error_code", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}