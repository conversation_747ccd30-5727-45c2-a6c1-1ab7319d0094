/// Augment 重置功能
/// 对应原版 Python 的 AugmentReset 类

use std::collections::HashMap;
use std::path::Path;
use serde::{Serialize, Deserialize};
use crate::utils::progress::ProgressReporter;
use crate::tools::cursor::{
    paths as cursor_paths,
    json_modifier as cursor_json,
    sqlite_modifier as cursor_sqlite,
    workspace_cleaner as cursor_workspace,
};
use crate::tools::vscode::{
    paths as vscode_paths,
    json_modifier as vscode_json,
    sqlite_modifier as vscode_sqlite,
    workspace_cleaner as vscode_workspace,
};

/// 重置步骤结果
/// 对应原版 Python 的 results 列表中的字典项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResetStepResult {
    pub step: String,
    pub success: bool,
    pub error: Option<String>,
    // 修改遥测ID步骤的特定字段
    pub old_machine_id: Option<String>,
    pub new_machine_id: Option<String>,
    pub old_device_id: Option<String>,
    pub new_device_id: Option<String>,
    // 清理数据库步骤的特定字段
    pub deleted_rows: Option<usize>,
    // 清理工作区步骤的特定字段
    pub deleted_files: Option<usize>,
    // 备份路径
    pub backup_path: Option<String>,
}

/// 重置结果
/// 对应原版 Python 的 reset() 方法返回值
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResetResult {
    pub success: bool,
    pub results: Vec<ResetStepResult>,
}

/// 通用的修改结果数据
/// 用于统一 Cursor 和 VSCode 的 ModifyResult
#[derive(Debug, Clone)]
pub struct ModifyResultData {
    pub old_machine_id: String,
    pub new_machine_id: String,
    pub old_device_id: String,
    pub new_device_id: String,
    pub storage_backup_path: String,
}

/// 通用的清理结果数据
/// 用于统一 Cursor 和 VSCode 的 CleanResult
#[derive(Debug, Clone)]
pub struct CleanResultData {
    pub deleted_rows: usize,
    pub db_backup_path: String,
}

/// 通用的工作区清理结果数据
/// 用于统一 Cursor 和 VSCode 的 CleanWorkspaceResult
#[derive(Debug, Clone)]
pub struct CleanWorkspaceResultData {
    pub deleted_files_count: usize,
    pub backup_path: Option<String>,
}

/// Augment 重置管理器
/// 对应原版 Python 的 AugmentReset 类
#[derive(Debug)]
pub struct AugmentReset {
    editor_type: String,
    progress_reporter: ProgressReporter,
}

impl AugmentReset {
    /// 创建新的重置管理器
    /// 对应原版 Python 的 __init__ 方法
    pub fn new(editor_type: &str) -> Result<Self, String> {
        let editor_type = editor_type.to_lowercase();

        // 验证编辑器类型
        let supported_editors = [
            "cursor", "vscode",
            "intellij", "pycharm", "webstorm", "phpstorm",
            "rubymine", "clion", "goland", "rider",
            "datagrip", "androidstudio"
        ];
        if !supported_editors.contains(&editor_type.as_str()) {
            return Err(format!("不支持的编辑器类型: {}，支持的类型: {}", editor_type, supported_editors.join(", ")));
        }

        Ok(Self {
            editor_type,
            progress_reporter: ProgressReporter::new(),
        })
    }

    /// 设置进度回调函数
    /// 对应原版 Python 的 set_progress_callback 方法
    pub fn set_progress_callback<F>(&mut self, callback: F)
    where
        F: Fn(&str, f64) + Send + Sync + 'static,
    {
        self.progress_reporter.set_callback(Box::new(callback));
    }

    /// 报告进度
    /// 对应原版 Python 的 _report_progress 方法
    fn report_progress(&self, message: &str, progress: f64) {
        self.progress_reporter.report(message, progress);
    }

    /// 获取所有相关路径
    /// 对应原版 Python 的 get_all_paths 方法
    pub fn get_all_paths(&self) -> HashMap<String, String> {
        let mut paths = HashMap::new();

        match self.editor_type.as_str() {
            "cursor" => {
                paths.insert("storage".to_string(), cursor_paths::get_storage_path());
                paths.insert("db".to_string(), cursor_paths::get_db_path());
                paths.insert("machine_id".to_string(), cursor_paths::get_machine_id_path());
                paths.insert("workspace_storage".to_string(), cursor_paths::get_workspace_storage_path());
            }
            "vscode" => {
                paths.insert("storage".to_string(), vscode_paths::get_storage_path());
                paths.insert("db".to_string(), vscode_paths::get_db_path());
                paths.insert("machine_id".to_string(), vscode_paths::get_machine_id_path());
                paths.insert("workspace_storage".to_string(), vscode_paths::get_workspace_storage_path());
            }
            "intellij" | "pycharm" | "webstorm" | "phpstorm" | "rubymine" | "clion" | "goland" | "rider" | "datagrip" | "androidstudio" => {
                use crate::tools::jetbrains::paths::{get_jetbrains_config_dir, get_permanent_device_id_path, get_permanent_user_id_path};

                if let Some(config_dir) = get_jetbrains_config_dir() {
                    paths.insert("config_dir".to_string(), config_dir.to_string_lossy().to_string());
                } else {
                    paths.insert("config_dir".to_string(), "JetBrains配置目录未找到".to_string());
                }

                if let Some(device_id_path) = get_permanent_device_id_path() {
                    paths.insert("device_id".to_string(), device_id_path.to_string_lossy().to_string());
                } else {
                    paths.insert("device_id".to_string(), "设备ID文件路径未找到".to_string());
                }

                if let Some(user_id_path) = get_permanent_user_id_path() {
                    paths.insert("user_id".to_string(), user_id_path.to_string_lossy().to_string());
                } else {
                    paths.insert("user_id".to_string(), "用户ID文件路径未找到".to_string());
                }

                // 对于JetBrains系列，我们使用config_dir作为主要的"storage"检查
                paths.insert("storage".to_string(), paths.get("config_dir").unwrap_or(&"未知".to_string()).clone());
            }
            _ => {
                // 这种情况在构造函数中已经检查过，不应该发生
                paths.insert("error".to_string(), format!("不支持的编辑器类型: {}", self.editor_type));
            }
        }

        paths
    }

    /// 检查相关文件是否存在
    /// 对应原版 Python 的 check_files_exist 方法
    pub fn check_files_exist(&self) -> HashMap<String, bool> {
        let mut results = HashMap::new();
        let paths = self.get_all_paths();

        for (name, path) in paths {
            if path.starts_with("错误") {
                results.insert(name, false);
            } else {
                results.insert(name, Path::new(&path).exists());
            }
        }

        results
    }

    /// 执行重置操作
    /// 对应原版 Python 的 reset 方法
    pub async fn reset(&self) -> ResetResult {
        let mut results = Vec::new();
        let total_steps = 4; // 预检查 + 3个重置步骤
        let mut current_step = 0;

        // 步骤0: 预检查
        self.report_progress("检查文件状态...", current_step as f64 / total_steps as f64);
        let file_status = self.check_files_exist();
        let paths = self.get_all_paths();

        // 检查关键文件是否存在
        if !file_status.get("storage").unwrap_or(&false) {
            let storage_path = paths.get("storage").unwrap_or(&"未知".to_string()).clone();
            let error_msg = format!("存储文件不存在: {}", storage_path);
            results.push(ResetStepResult {
                step: "预检查".to_string(),
                success: false,
                error: Some(error_msg),
                old_machine_id: None,
                new_machine_id: None,
                old_device_id: None,
                new_device_id: None,
                deleted_rows: None,
                deleted_files: None,
                backup_path: None,
            });
            return ResetResult {
                success: false,
                results,
            };
        }

        current_step += 1;

        // 步骤1: 修改遥测ID
        self.report_progress("正在修改遥测ID...", current_step as f64 / total_steps as f64);
        match self.modify_telemetry_ids().await {
            Ok(id_result) => {
                results.push(ResetStepResult {
                    step: "修改遥测ID".to_string(),
                    success: true,
                    error: None,
                    old_machine_id: Some(id_result.old_machine_id),
                    new_machine_id: Some(id_result.new_machine_id),
                    old_device_id: Some(id_result.old_device_id),
                    new_device_id: Some(id_result.new_device_id),
                    deleted_rows: None,
                    deleted_files: None,
                    backup_path: Some(id_result.storage_backup_path),
                });
            }
            Err(e) => {
                results.push(ResetStepResult {
                    step: "修改遥测ID".to_string(),
                    success: false,
                    error: Some(e),
                    old_machine_id: None,
                    new_machine_id: None,
                    old_device_id: None,
                    new_device_id: None,
                    deleted_rows: None,
                    deleted_files: None,
                    backup_path: None,
                });
                return ResetResult {
                    success: false,
                    results,
                };
            }
        }

        current_step += 1;

        // 步骤2: 清理数据库
        self.report_progress("正在清理数据库...", current_step as f64 / total_steps as f64);
        match self.clean_database().await {
            Ok(db_result) => {
                results.push(ResetStepResult {
                    step: "清理数据库".to_string(),
                    success: true,
                    error: None,
                    old_machine_id: None,
                    new_machine_id: None,
                    old_device_id: None,
                    new_device_id: None,
                    deleted_rows: Some(db_result.deleted_rows),
                    deleted_files: None,
                    backup_path: Some(db_result.db_backup_path),
                });
            }
            Err(e) => {
                results.push(ResetStepResult {
                    step: "清理数据库".to_string(),
                    success: false,
                    error: Some(e),
                    old_machine_id: None,
                    new_machine_id: None,
                    old_device_id: None,
                    new_device_id: None,
                    deleted_rows: None,
                    deleted_files: None,
                    backup_path: None,
                });
                // 继续执行，不中断（与原版 Python 行为一致）
            }
        }

        current_step += 1;

        // 步骤3: 清理工作区存储
        self.report_progress("正在清理工作区存储...", current_step as f64 / total_steps as f64);

        // 添加更详细的进度报告
        self.report_progress("正在扫描工作区文件...", (current_step as f64 + 0.1) / total_steps as f64);

        match self.clean_workspace().await {
            Ok(ws_result) => {
                // 报告删除进度
                if ws_result.deleted_files_count > 0 {
                    self.report_progress(
                        &format!("已清理 {} 个文件", ws_result.deleted_files_count),
                        (current_step as f64 + 0.8) / total_steps as f64
                    );
                }

                results.push(ResetStepResult {
                    step: "清理工作区存储".to_string(),
                    success: true,
                    error: None,
                    old_machine_id: None,
                    new_machine_id: None,
                    old_device_id: None,
                    new_device_id: None,
                    deleted_rows: None,
                    deleted_files: Some(ws_result.deleted_files_count),
                    backup_path: ws_result.backup_path,
                });
            }
            Err(e) => {
                self.report_progress("工作区清理失败", (current_step as f64 + 0.5) / total_steps as f64);
                results.push(ResetStepResult {
                    step: "清理工作区存储".to_string(),
                    success: false,
                    error: Some(e),
                    old_machine_id: None,
                    new_machine_id: None,
                    old_device_id: None,
                    new_device_id: None,
                    deleted_rows: None,
                    deleted_files: None,
                    backup_path: None,
                });
            }
        }

        // current_step += 1; // 最后一步，不需要更新
        self.report_progress("重置完成", 1.0);

        // 检查是否所有步骤都成功
        let all_success = results.iter().all(|result| result.success);

        ResetResult {
            success: all_success,
            results,
        }
    }

    /// 修改遥测ID
    /// 对应原版 Python 中调用 modify_ids 函数
    async fn modify_telemetry_ids(&self) -> Result<ModifyResultData, String> {
        match self.editor_type.as_str() {
            "cursor" => {
                let result = cursor_json::modify_telemetry_ids()?;
                Ok(ModifyResultData {
                    old_machine_id: result.old_machine_id,
                    new_machine_id: result.new_machine_id,
                    old_device_id: result.old_device_id,
                    new_device_id: result.new_device_id,
                    storage_backup_path: result.storage_backup_path,
                })
            }
            "vscode" => {
                let result = vscode_json::modify_telemetry_ids()?;
                Ok(ModifyResultData {
                    old_machine_id: result.old_machine_id,
                    new_machine_id: result.new_machine_id,
                    old_device_id: result.old_device_id,
                    new_device_id: result.new_device_id,
                    storage_backup_path: result.storage_backup_path,
                })
            }
            "intellij" | "pycharm" | "webstorm" | "phpstorm" | "rubymine" | "clion" | "goland" | "rider" | "datagrip" | "androidstudio" => {
                use crate::tools::jetbrains::id_modifier::modify_jetbrains_ids;
                let result = modify_jetbrains_ids()?;
                Ok(ModifyResultData {
                    old_machine_id: result.old_device_id,  // JetBrains使用设备ID作为机器ID
                    new_machine_id: result.new_device_id,
                    old_device_id: result.old_user_id,     // 用户ID作为设备ID
                    new_device_id: result.new_user_id,
                    storage_backup_path: result.device_id_backup_path.or(result.user_id_backup_path).unwrap_or_default(),
                })
            }
            _ => Err(format!("不支持的编辑器类型: {}", self.editor_type)),
        }
    }

    /// 清理数据库
    /// 对应原版 Python 中调用 clean_db 函数
    async fn clean_database(&self) -> Result<CleanResultData, String> {
        match self.editor_type.as_str() {
            "cursor" => {
                let result = cursor_sqlite::clean_augment_data()?;
                Ok(CleanResultData {
                    deleted_rows: result.deleted_rows,
                    db_backup_path: result.db_backup_path,
                })
            }
            "vscode" => {
                let result = vscode_sqlite::clean_augment_data()?;
                Ok(CleanResultData {
                    deleted_rows: result.deleted_rows,
                    db_backup_path: result.db_backup_path,
                })
            }
            "intellij" | "pycharm" | "webstorm" | "phpstorm" | "rubymine" | "clion" | "goland" | "rider" | "datagrip" | "androidstudio" => {
                // JetBrains系列不需要数据库清理，但我们可以锁定文件
                use crate::tools::jetbrains::file_locker::lock_jetbrains_files;
                let _lock_result = lock_jetbrains_files()?;
                Ok(CleanResultData {
                    deleted_rows: 0, // JetBrains没有数据库记录需要删除
                    db_backup_path: "JetBrains不使用数据库".to_string(),
                })
            }
            _ => Err(format!("不支持的编辑器类型: {}", self.editor_type)),
        }
    }

    /// 清理工作区存储
    /// 对应原版 Python 中调用 clean_workspace 函数
    async fn clean_workspace(&self) -> Result<CleanWorkspaceResultData, String> {
        match self.editor_type.as_str() {
            "cursor" => {
                let result = cursor_workspace::clean_workspace_storage()?;
                Ok(CleanWorkspaceResultData {
                    deleted_files_count: result.deleted_files_count,
                    backup_path: result.backup_path,
                })
            }
            "vscode" => {
                let result = vscode_workspace::clean_workspace_storage()?;
                Ok(CleanWorkspaceResultData {
                    deleted_files_count: result.deleted_files_count,
                    backup_path: result.backup_path,
                })
            }
            "intellij" | "pycharm" | "webstorm" | "phpstorm" | "rubymine" | "clion" | "goland" | "rider" | "datagrip" | "androidstudio" => {
                // JetBrains系列不需要工作区清理，ID文件已经在前面的步骤中处理了
                Ok(CleanWorkspaceResultData {
                    deleted_files_count: 0, // JetBrains没有工作区文件需要删除
                    backup_path: Some("JetBrains不需要工作区清理".to_string()),
                })
            }
            _ => Err(format!("不支持的编辑器类型: {}", self.editor_type)),
        }
    }

    /// 生成重置结果摘要
    /// 对应原版 Python 的 get_reset_summary 方法
    pub fn get_reset_summary(&self, results: &[ResetStepResult]) -> String {
        let mut summary_lines = Vec::new();
        summary_lines.push(format!("重置 {} Augment 结果:\n", self.editor_type.to_uppercase()));

        for result in results {
            let step = &result.step;
            if result.success {
                summary_lines.push(format!("✅ {}: 成功", step));

                if step == "修改遥测ID" {
                    if let Some(ref old_id) = result.old_machine_id {
                        summary_lines.push(format!("   旧机器ID: {}", old_id));
                    }
                    if let Some(ref new_id) = result.new_machine_id {
                        summary_lines.push(format!("   新机器ID: {}", new_id));
                    }
                } else if step == "清理数据库" {
                    if let Some(deleted_rows) = result.deleted_rows {
                        summary_lines.push(format!("   删除了 {} 行数据", deleted_rows));
                    }
                } else if step == "清理工作区存储" {
                    if let Some(deleted_files) = result.deleted_files {
                        summary_lines.push(format!("   删除了 {} 个文件", deleted_files));
                    }
                }

                if let Some(ref backup_path) = result.backup_path {
                    summary_lines.push(format!("   备份路径: {}", backup_path));
                }
            } else {
                summary_lines.push(format!("❌ {}: 失败", step));
                if let Some(ref error) = result.error {
                    summary_lines.push(format!("   错误: {}", error));
                }
            }
        }

        summary_lines.join("\n")
    }
}
