{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"futures\", \"futures-core\"]", "target": 1703982665153516621, "profile": 15657897354478470176, "path": 4433592453032207665, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sync_wrapper-43a506a8a129076d\\dep-lib-sync_wrapper", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}