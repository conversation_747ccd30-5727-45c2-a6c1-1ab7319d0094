{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"nightly\"]", "target": 4721033718741301145, "profile": 15657897354478470176, "path": 2759825085283882432, "deps": [[2828590642173593838, "cfg_if", false, 16048148996252698940]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\thread_local-49c8114b3250a1fd\\dep-lib-thread_local", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}