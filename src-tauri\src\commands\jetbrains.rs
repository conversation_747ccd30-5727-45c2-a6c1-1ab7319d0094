/// JetBrains 编辑器命令
/// 
/// 提供 Tauri 命令用于前端调用JetBrains相关功能
/// 包括ID修改、文件锁定、状态检查等

use tauri::{command, State};
use crate::app_state::AppState;
use crate::tools::jetbrains::{
    id_modifier::{modify_jetbrains_ids, check_jetbrains_id_status, JetBrainsModifyResult},
    file_locker::{lock_jetbrains_files, unlock_jetbrains_files, FileLockResult},
    paths::{get_jetbrains_info, is_jetbrains_installed},
    process_terminator::{terminate_jetbrains_processes, has_running_jetbrains_processes},
};
use crate::tools::process_terminator::ProcessTerminationResult;

/// 修改JetBrains遥测ID
/// 对应 augment-vip-rust-master 的 update_id_file 功能
/// 
/// 此命令：
/// 1. 生成新的设备ID和用户ID
/// 2. 备份原有ID文件
/// 3. 更新ID文件内容
/// 
/// Returns:
///     Result<JetBrainsModifyResult, String>: 包含操作结果的结构体或错误信息
#[command]
pub async fn jetbrains_modify_ids(
    _state: State<'_, AppState>
) -> Result<JetBrainsModifyResult, String> {
    modify_jetbrains_ids()
}

/// 锁定JetBrains ID文件
/// 防止IDE重新生成ID文件
/// 
/// 此命令：
/// 1. 设置ID文件为只读
/// 2. 使用平台特定的锁定方法
/// 3. 返回锁定结果
/// 
/// Returns:
///     Result<FileLockResult, String>: 包含锁定结果的结构体或错误信息
#[command]
pub async fn jetbrains_lock_files(
    _state: State<'_, AppState>
) -> Result<FileLockResult, String> {
    lock_jetbrains_files()
}

/// 解锁JetBrains ID文件
/// 移除文件的只读属性
/// 
/// Returns:
///     Result<Vec<String>, String>: 解锁的文件列表或错误信息
#[command]
pub async fn jetbrains_unlock_files(
    _state: State<'_, AppState>
) -> Result<Vec<String>, String> {
    unlock_jetbrains_files()
}

/// 检查JetBrains ID文件状态
/// 返回ID文件的详细信息
/// 
/// Returns:
///     Result<serde_json::Value, String>: ID文件状态信息或错误信息
#[command]
pub async fn jetbrains_check_status(
    _state: State<'_, AppState>
) -> Result<serde_json::Value, String> {
    check_jetbrains_id_status()
}

/// 检查JetBrains是否已安装
/// 
/// Returns:
///     Result<bool, String>: 是否安装JetBrains IDE
#[command]
pub async fn jetbrains_is_installed(
    _state: State<'_, AppState>
) -> Result<bool, String> {
    Ok(is_jetbrains_installed())
}

/// 获取JetBrains安装信息
/// 
/// Returns:
///     Result<serde_json::Value, String>: JetBrains安装信息
#[command]
pub async fn jetbrains_get_info(
    _state: State<'_, AppState>
) -> Result<serde_json::Value, String> {
    let (installed, path) = get_jetbrains_info();
    Ok(serde_json::json!({
        "installed": installed,
        "config_path": path
    }))
}

/// 终止JetBrains IDE进程
/// 对应 augment-vip-rust-master 的 terminate_ides 功能
///
/// 此命令：
/// 1. 查找所有正在运行的JetBrains IDE进程
/// 2. 终止这些进程及其父进程
/// 3. 返回终止结果
///
/// Returns:
///     Result<ProcessTerminationResult, String>: 包含终止结果的结构体或错误信息
#[command]
pub async fn jetbrains_terminate_processes(
    _state: State<'_, AppState>
) -> Result<ProcessTerminationResult, String> {
    terminate_jetbrains_processes()
}

/// 检查是否有JetBrains进程正在运行
///
/// Returns:
///     Result<bool, String>: 如果有进程正在运行则返回true
#[command]
pub async fn jetbrains_check_running_processes(
    _state: State<'_, AppState>
) -> Result<bool, String> {
    Ok(has_running_jetbrains_processes())
}

/// JetBrains完整重置结果结构体
#[derive(Debug, Clone, serde::Serialize)]
pub struct JetBrainsResetResult {
    pub termination_result: ProcessTerminationResult,
    pub modify_result: JetBrainsModifyResult,
    pub lock_result: FileLockResult,
}

/// 执行完整的JetBrains重置流程
/// 对应 augment-vip-rust-master 的完整重置逻辑
///
/// 此命令按顺序执行：
/// 1. 终止正在运行的JetBrains IDE进程
/// 2. 修改遥测ID
/// 3. 锁定ID文件
///
/// Returns:
///     Result<JetBrainsResetResult, String>: 包含所有操作结果的结构体或错误信息
#[command]
pub async fn jetbrains_full_reset(
    _state: State<'_, AppState>
) -> Result<JetBrainsResetResult, String> {
    // 1. 终止正在运行的JetBrains IDE进程（对应原始项目的terminate_ides）
    let termination_result = terminate_jetbrains_processes()?;

    // 2. 修改遥测ID
    let modify_result = modify_jetbrains_ids()?;

    // 3. 锁定ID文件
    let lock_result = lock_jetbrains_files()?;

    Ok(JetBrainsResetResult {
        termination_result,
        modify_result,
        lock_result,
    })
}
