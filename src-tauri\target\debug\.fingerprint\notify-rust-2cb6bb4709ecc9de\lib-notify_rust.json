{"rustc": 12488743700189009532, "features": "[\"async\", \"default\", \"serde\", \"z\", \"zbus\"]", "declared_features": "[\"async\", \"chrono\", \"d\", \"d_vendored\", \"dbus\", \"debug_namespace\", \"default\", \"env_logger\", \"image\", \"images\", \"lazy_static\", \"serde\", \"z\", \"zbus\"]", "target": 2563646654602359118, "profile": 15657897354478470176, "path": 4996698989076385775, "deps": [[2494618262354031007, "winrt_notification", false, 9927024139182373581], [7425331225454150061, "futures_lite", false, 1970365380875272917]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\notify-rust-2cb6bb4709ecc9de\\dep-lib-notify_rust", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}