{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"default\", \"serde\", \"std\", \"use_std\"]", "target": 17124342308084364240, "profile": 15657897354478470176, "path": 14570661446661309732, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\either-7479c47a575ca1af\\dep-lib-either", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}