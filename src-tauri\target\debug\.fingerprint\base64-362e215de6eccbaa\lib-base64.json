{"rustc": 12488743700189009532, "features": "[\"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"std\"]", "target": 13060062996227388079, "profile": 15657897354478470176, "path": 16147029485795931013, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\base64-362e215de6ecc<PERSON>a\\dep-lib-base64", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}