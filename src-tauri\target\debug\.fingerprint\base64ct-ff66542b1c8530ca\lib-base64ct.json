{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"alloc\", \"std\"]", "target": 15548948006327107948, "profile": 15657897354478470176, "path": 7743559341386312522, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\base64ct-ff66542b1c8530ca\\dep-lib-base64ct", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}