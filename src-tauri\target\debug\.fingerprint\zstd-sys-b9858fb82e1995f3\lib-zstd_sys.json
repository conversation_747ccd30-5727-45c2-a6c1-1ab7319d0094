{"rustc": 12488743700189009532, "features": "[\"legacy\", \"std\", \"zdict_builder\"]", "declared_features": "[\"bindgen\", \"debug\", \"default\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"no_wasm_shim\", \"non-cargo\", \"pkg-config\", \"seekable\", \"std\", \"thin\", \"thin-lto\", \"zdict_builder\", \"zstdmt\"]", "target": 3822121216239517979, "profile": 4758648406898424494, "path": 11044797738679661841, "deps": [[8373447648276846408, "build_script_build", false, 17831601101948260846]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zstd-sys-b9858fb82e1995f3\\dep-lib-zstd_sys", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}