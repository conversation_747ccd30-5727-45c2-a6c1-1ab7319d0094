{"rustc": 12488743700189009532, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"modern-full\", \"modern_sqlite\", \"release_memory\", \"serde_json\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"winsqlite3\", \"with-asan\"]", "target": 1382081207361365847, "profile": 15657897354478470176, "path": 470804960101826431, "deps": [[1067686709987796982, "libsqlite3_sys", false, 15188512296479378601], [3405817021026194662, "hashlink", false, 8154432509624488177], [3666196340704888985, "smallvec", false, 3450016790407122536], [5510864063823219921, "fallible_streaming_iterator", false, 11693106409798994337], [7896293946984509699, "bitflags", false, 6472482513997909962], [17725626451704002459, "fallible_iterator", false, 4602200468645728475]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rusqlite-3406c0669cb8dc5c\\dep-lib-rusqlite", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}