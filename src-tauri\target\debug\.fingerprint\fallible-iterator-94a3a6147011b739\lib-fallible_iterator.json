{"rustc": 12488743700189009532, "features": "[\"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"std\"]", "target": 15245709686714427328, "profile": 15657897354478470176, "path": 7662715435395035923, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\fallible-iterator-94a3a6147011b739\\dep-lib-fallible_iterator", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}