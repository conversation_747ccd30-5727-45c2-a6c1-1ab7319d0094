{"rustc": 12488743700189009532, "features": "[\"default\", \"idna\", \"punycode\"]", "declared_features": "[\"anycase\", \"default\", \"hashbrown\", \"idna\", \"punycode\", \"std\", \"unicase\"]", "target": 5230192483030915385, "profile": 15657897354478470176, "path": 3293025836116055432, "deps": [[6376232718484714452, "idna", false, 12452571005013643364], [9920160603812655370, "psl_types", false, 5367746585954255058]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\publicsuffix-eee4e18370265730\\dep-lib-publicsuffix", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}