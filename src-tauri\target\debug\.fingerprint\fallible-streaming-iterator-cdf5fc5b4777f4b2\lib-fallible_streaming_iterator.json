{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"std\"]", "target": 16001337131876932863, "profile": 15657897354478470176, "path": 10128968742914719289, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\fallible-streaming-iterator-cdf5fc5b4777f4b2\\dep-lib-fallible_streaming_iterator", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}