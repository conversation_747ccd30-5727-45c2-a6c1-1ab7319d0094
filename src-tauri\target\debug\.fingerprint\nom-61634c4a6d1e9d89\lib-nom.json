{"rustc": 12488743700189009532, "features": "[\"alloc\", \"default\", \"lexical\", \"lexical-core\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"lazy_static\", \"lexical\", \"lexical-core\", \"regex\", \"regexp\", \"regexp_macros\", \"std\"]", "target": 18179990377595040139, "profile": 15657897354478470176, "path": 7916364412437035383, "deps": [[10578342194672938417, "build_script_build", false, 11048127493704336176], [15932120279885307830, "memchr", false, 11808295126443545459], [17462531364616038361, "lexical_core", false, 7515624154452605047]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\nom-61634c4a6d1e9d89\\dep-lib-nom", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}