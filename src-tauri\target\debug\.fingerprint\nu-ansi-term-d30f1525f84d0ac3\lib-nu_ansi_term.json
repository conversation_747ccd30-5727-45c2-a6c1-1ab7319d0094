{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"derive_serde_style\", \"serde\"]", "target": 6750653021751799497, "profile": 15657897354478470176, "path": 9122283339567536603, "deps": [[9439046465659389995, "overload", false, 16738037744938138208], [10020888071089587331, "<PERSON>ap<PERSON>", false, 4985919973182291098]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\nu-ansi-term-d30f1525f84d0ac3\\dep-lib-nu_ansi_term", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}