{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"std\"]", "target": 15439925696471062677, "profile": 15657897354478470176, "path": 13245609710729268661, "deps": [[2352660017780662552, "crypto_common", false, 2801516405637167640], [17003143334332120809, "subtle", false, 12027079904136647321]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\universal-hash-b09c09014a209cec\\dep-lib-universal_hash", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}