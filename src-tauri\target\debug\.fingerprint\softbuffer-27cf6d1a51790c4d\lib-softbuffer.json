{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 2623970234580381501, "deps": [[376837177317575824, "build_script_build", false, 13936019942585475768], [4143744114649553716, "raw_window_handle", false, 5439867478843892220], [5986029879202738730, "log", false, 6744974836633744950], [10281541584571964250, "windows_sys", false, 5574740418577519391]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-27cf6d1a51790c4d\\dep-lib-softbuffer", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}