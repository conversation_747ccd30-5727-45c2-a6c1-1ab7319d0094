{"rustc": 12488743700189009532, "features": "[\"default\", \"hmac\"]", "declared_features": "[\"default\", \"hmac\", \"parallel\", \"password-hash\", \"rayon\", \"sha1\", \"sha2\", \"simple\", \"std\"]", "target": 9229284490985355380, "profile": 15657897354478470176, "path": 6712279298308330607, "deps": [[9209347893430674936, "hmac", false, 13475496206054761251], [17475753849556516473, "digest", false, 2215462883847041727]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pbkdf2-4aa7497e349d1e00\\dep-lib-pbkdf2", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}