{"rustc": 12488743700189009532, "features": "[\"client\", \"h2\", \"http1\", \"http2\", \"runtime\", \"socket2\", \"tcp\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"backports\", \"client\", \"default\", \"deprecated\", \"ffi\", \"full\", \"h2\", \"http1\", \"http2\", \"libc\", \"nightly\", \"runtime\", \"server\", \"socket2\", \"stream\", \"tcp\"]", "target": 5299595107718448861, "profile": 15657897354478470176, "path": 7407622306368488632, "deps": [[784494742817713399, "tower_service", false, 10293051206577528705], [1569313478171189446, "want", false, 7726210540806541759], [1811549171721445101, "futures_channel", false, 12844589627770586958], [1906322745568073236, "pin_project_lite", false, 377786499476956345], [4405182208873388884, "http", false, 18286749706990962492], [6163892036024256188, "httparse", false, 14180267967234935200], [6304235478050270880, "httpdate", false, 10525808790923342559], [7620660491849607393, "futures_core", false, 4949676933428321037], [7695812897323945497, "itoa", false, 7427843300699540761], [8606274917505247608, "tracing", false, 11945251338150654124], [8915503303801890683, "http_body", false, 15940265720583617557], [10629569228670356391, "futures_util", false, 18681567327667426], [12393800526703971956, "tokio", false, 13798810526153862019], [12614995553916589825, "socket2", false, 8204517071945993268], [13809605890706463735, "h2", false, 4021519316968728553], [16066129441945555748, "bytes", false, 14972860757458678711]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-7864dba82d67bd5c\\dep-lib-hyper", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}