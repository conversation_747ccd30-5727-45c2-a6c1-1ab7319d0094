{"rustc": 12488743700189009532, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 8076760474669953771, "deps": [[1573238666360410412, "rand_chacha", false, 9059750967107393377], [18130209639506977569, "rand_core", false, 3865031145985889137]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-36a82d284bc0287c\\dep-lib-rand", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}