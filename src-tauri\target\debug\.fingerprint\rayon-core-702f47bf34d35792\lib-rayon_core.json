{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"web_spin_lock\"]", "target": 9103977627086248499, "profile": 15657897354478470176, "path": 15836383199186475799, "deps": [[4468123440088164316, "crossbeam_utils", false, 11134732234113234572], [9705675356647965917, "build_script_build", false, 14076552333702891257], [17472578983440242455, "crossbeam_deque", false, 13130720569762373780]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rayon-core-702f47bf34d35792\\dep-lib-rayon_core", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}