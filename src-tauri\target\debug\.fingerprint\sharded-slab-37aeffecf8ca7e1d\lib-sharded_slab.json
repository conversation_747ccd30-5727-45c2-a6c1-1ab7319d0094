{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"loom\"]", "target": 12629115416767553567, "profile": 15657897354478470176, "path": 3401342829405704270, "deps": [[17917672826516349275, "lazy_static", false, 10688309873950042378]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sharded-slab-37aeffecf8ca7e1d\\dep-lib-sharded_slab", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}