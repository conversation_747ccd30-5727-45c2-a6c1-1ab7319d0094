{"rustc": 12488743700189009532, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 10594997556642427531, "deps": [[40386456601120721, "percent_encoding", false, 348877878830084439], [1200537532907108615, "url<PERSON><PERSON>n", false, 8957199841052230058], [2013030631243296465, "webview2_com", false, 12481653937944749380], [2671782512663819132, "tauri_utils", false, 501098159936826022], [3150220818285335163, "url", false, 6062674917897409888], [3331586631144870129, "getrandom", false, 8860980642802204872], [4143744114649553716, "raw_window_handle", false, 5439867478843892220], [4494683389616423722, "muda", false, 15043917235006381706], [4919829919303820331, "serialize_to_javascript", false, 17321715089052899838], [5986029879202738730, "log", false, 6744974836633744950], [6089812615193535349, "tauri_runtime", false, 13062917935313888369], [7573826311589115053, "tauri_macros", false, 14503509031399977443], [9010263965687315507, "http", false, 3514203428925704760], [9689903380558560274, "serde", false, 12662985874143964422], [10229185211513642314, "mime", false, 7552261498337461961], [10806645703491011684, "thiserror", false, 14032229823463785456], [11599800339996261026, "tauri_runtime_wry", false, 13597478825707473438], [11989259058781683633, "dunce", false, 1785783859457516269], [12393800526703971956, "tokio", false, 13798810526153862019], [12565293087094287914, "window_vibrancy", false, 14903130024013301927], [12986574360607194341, "serde_repr", false, 6966888983837873276], [13077543566650298139, "heck", false, 9099268929377437097], [13625485746686963219, "anyhow", false, 17047174968382787859], [14039947826026167952, "build_script_build", false, 7178447759297261249], [14585479307175734061, "windows", false, 1621046816081544285], [15367738274754116744, "serde_json", false, 14741482141302888685], [16928111194414003569, "dirs", false, 11644794514588536626], [17155886227862585100, "glob", false, 4678781086715643041]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-a8262743ec8aa369\\dep-lib-tauri", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}