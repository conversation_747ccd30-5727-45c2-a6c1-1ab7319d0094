{"rustc": 12488743700189009532, "features": "[\"rand_core\"]", "declared_features": "[\"alloc\", \"default\", \"rand_core\", \"std\"]", "target": 5392353821897108982, "profile": 15657897354478470176, "path": 12998527411825671841, "deps": [[13036989088902834928, "base64ct", false, 5001438322088335697], [17003143334332120809, "subtle", false, 12027079904136647321], [18130209639506977569, "rand_core", false, 3865031145985889137]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\password-hash-27b64740d3aa582a\\dep-lib-password_hash", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}