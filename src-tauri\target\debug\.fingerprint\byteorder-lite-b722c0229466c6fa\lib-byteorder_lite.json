{"rustc": 12488743700189009532, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"std\"]", "target": 13691508551864173732, "profile": 15657897354478470176, "path": 18346466060376883436, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\byteorder-lite-b722c0229466c6fa\\dep-lib-byteorder_lite", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}