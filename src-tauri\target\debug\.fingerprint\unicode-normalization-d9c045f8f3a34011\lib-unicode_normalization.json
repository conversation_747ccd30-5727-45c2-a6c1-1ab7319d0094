{"rustc": 12488743700189009532, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"std\"]", "target": 8830255594621478391, "profile": 15657897354478470176, "path": 14805023275273408761, "deps": [[1042707345065476716, "tinyvec", false, 7738093923076585369]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\unicode-normalization-d9c045f8f3a34011\\dep-lib-unicode_normalization", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}