{"rustc": 12488743700189009532, "features": "[\"alloc\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"js\", \"std\"]", "target": 9543367341069791401, "profile": 15657897354478470176, "path": 9972820509298175543, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\fastrand-a99a7506000034a9\\dep-lib-fastrand", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}