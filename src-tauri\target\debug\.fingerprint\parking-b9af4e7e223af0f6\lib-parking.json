{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"loom\"]", "target": 9855717379987801857, "profile": 15657897354478470176, "path": 5326338894875753565, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\parking-b9af4e7e223af0f6\\dep-lib-parking", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}