{"rustc": 12488743700189009532, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"std\", \"unstable\"]", "target": 5742820543410686210, "profile": 13318305459243126790, "path": 17403648724006220370, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-io-979ba55f24fce8c9\\dep-lib-futures_io", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}