{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"alloc\", \"block-padding\", \"std\", \"zeroize\"]", "target": 4643697310696577575, "profile": 15657897354478470176, "path": 2106998169735609608, "deps": [[7916416211798676886, "cipher", false, 13512874754309562015]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ctr-f3bd5b728a2222f9\\dep-lib-ctr", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}