["\\\\?\\E:\\UPanBeiFen\\YAugment\\YAugment\\src-tauri\\target\\debug\\build\\tauri-4217a9aaceb1d8e4\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\E:\\UPanBeiFen\\YAugment\\YAugment\\src-tauri\\target\\debug\\build\\tauri-4217a9aaceb1d8e4\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\E:\\UPanBeiFen\\YAugment\\YAugment\\src-tauri\\target\\debug\\build\\tauri-4217a9aaceb1d8e4\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\E:\\UPanBeiFen\\YAugment\\YAugment\\src-tauri\\target\\debug\\build\\tauri-4217a9aaceb1d8e4\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\E:\\UPanBeiFen\\YAugment\\YAugment\\src-tauri\\target\\debug\\build\\tauri-4217a9aaceb1d8e4\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\E:\\UPanBeiFen\\YAugment\\YAugment\\src-tauri\\target\\debug\\build\\tauri-4217a9aaceb1d8e4\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\E:\\UPanBeiFen\\YAugment\\YAugment\\src-tauri\\target\\debug\\build\\tauri-4217a9aaceb1d8e4\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\E:\\UPanBeiFen\\YAugment\\YAugment\\src-tauri\\target\\debug\\build\\tauri-4217a9aaceb1d8e4\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\E:\\UPanBeiFen\\YAugment\\YAugment\\src-tauri\\target\\debug\\build\\tauri-4217a9aaceb1d8e4\\out\\permissions\\path\\autogenerated\\default.toml"]