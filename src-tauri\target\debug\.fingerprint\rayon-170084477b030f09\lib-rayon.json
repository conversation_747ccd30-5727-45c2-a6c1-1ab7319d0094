{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"web_spin_lock\"]", "target": 16352802939243045765, "profile": 15657897354478470176, "path": 8123580029609154686, "deps": [[9705675356647965917, "rayon_core", false, 2796096259209670789], [12170264697963848012, "either", false, 5236247932137490992]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rayon-170084477b030f09\\dep-lib-rayon", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}