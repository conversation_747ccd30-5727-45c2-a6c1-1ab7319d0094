{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 2764086469773243511, "profile": 15657897354478470176, "path": 1039089432044428375, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\mime-52521697adef83d9\\dep-lib-mime", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}