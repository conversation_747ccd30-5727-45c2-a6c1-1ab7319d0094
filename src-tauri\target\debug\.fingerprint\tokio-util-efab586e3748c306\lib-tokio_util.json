{"rustc": 12488743700189009532, "features": "[\"codec\", \"default\", \"io\"]", "declared_features": "[\"__docs_rs\", \"codec\", \"compat\", \"default\", \"full\", \"futures-io\", \"futures-util\", \"hashbrown\", \"io\", \"io-util\", \"net\", \"rt\", \"slab\", \"time\", \"tracing\"]", "target": 17993092506817503379, "profile": 14976613090486706789, "path": 1842404388308326250, "deps": [[1906322745568073236, "pin_project_lite", false, 377786499476956345], [7013762810557009322, "futures_sink", false, 9855608131694143861], [7620660491849607393, "futures_core", false, 4949676933428321037], [12393800526703971956, "tokio", false, 13798810526153862019], [16066129441945555748, "bytes", false, 14972860757458678711]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tokio-util-efab586e3748c306\\dep-lib-tokio_util", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}