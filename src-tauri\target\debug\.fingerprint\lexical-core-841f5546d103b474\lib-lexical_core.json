{"rustc": 12488743700189009532, "features": "[\"arrayvec\", \"correct\", \"default\", \"ryu\", \"static_assertions\", \"std\", \"table\"]", "declared_features": "[\"arrayvec\", \"correct\", \"default\", \"dtoa\", \"format\", \"grisu3\", \"libm\", \"noinline\", \"property_tests\", \"proptest\", \"quickcheck\", \"radix\", \"rounding\", \"ryu\", \"static_assertions\", \"std\", \"table\", \"trim_floats\", \"unchecked_index\"]", "target": 15698117443813190224, "profile": 15657897354478470176, "path": 2781533319455184048, "deps": [[1216309103264968120, "ryu", false, 10196590457583298857], [2828590642173593838, "cfg_if", false, 16048148996252698940], [10435729446543529114, "bitflags", false, 1545659178466416193], [11279921689796057170, "arrayvec", false, 5715701010646114510], [13785866025199020095, "static_assertions", false, 18226519477758470789], [17462531364616038361, "build_script_build", false, 17556654544012479621]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\lexical-core-841f5546d103b474\\dep-lib-lexical_core", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}