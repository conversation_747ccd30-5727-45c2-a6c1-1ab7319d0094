{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 15657897354478470176, "path": 17611404771838775710, "deps": [[1462335029370885857, "quick_xml", false, 10496751673714872752], [3334271191048661305, "windows_version", false, 511316909870963320], [10806645703491011684, "thiserror", false, 14032229823463785456], [14585479307175734061, "windows", false, 1621046816081544285]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-808431eaa52a266f\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}