{"rustc": 12488743700189009532, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 15657897354478470176, "path": 16370540438701159118, "deps": [[966925859616469517, "ahash", false, 6024929898768223380], [9150530836556604396, "allocator_api2", false, 10274414722848401083]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-7bdc061e1f17d9ae\\dep-lib-hashbrown", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}