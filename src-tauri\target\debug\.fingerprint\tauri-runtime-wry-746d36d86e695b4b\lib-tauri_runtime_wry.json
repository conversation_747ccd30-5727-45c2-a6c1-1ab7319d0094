{"rustc": 12488743700189009532, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 6986511805275195493, "deps": [[376837177317575824, "softbuffer", false, 14940498550815098611], [2013030631243296465, "webview2_com", false, 12481653937944749380], [2671782512663819132, "tauri_utils", false, 501098159936826022], [3150220818285335163, "url", false, 6062674917897409888], [3722963349756955755, "once_cell", false, 3001542277175768057], [4143744114649553716, "raw_window_handle", false, 5439867478843892220], [5986029879202738730, "log", false, 6744974836633744950], [6089812615193535349, "tauri_runtime", false, 13062917935313888369], [8826339825490770380, "tao", false, 17639634942603127316], [9010263965687315507, "http", false, 3514203428925704760], [9141053277961803901, "wry", false, 5810812847496321143], [11599800339996261026, "build_script_build", false, 14891564592478290363], [14585479307175734061, "windows", false, 1621046816081544285]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-746d36d86e695b4b\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}