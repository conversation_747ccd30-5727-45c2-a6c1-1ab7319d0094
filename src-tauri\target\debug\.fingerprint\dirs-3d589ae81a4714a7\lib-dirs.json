{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 8852154185408534478, "profile": 15657897354478470176, "path": 11020964582703728365, "deps": [[11795441179928084356, "dirs_sys", false, 10383638467612515090]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\dirs-3d589ae81a4714a7\\dep-lib-dirs", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}