{"rustc": 12488743700189009532, "features": "[\"alloc\", \"default\", \"fastrand\", \"futures-io\", \"parking\", \"race\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"fastrand\", \"futures-io\", \"memchr\", \"parking\", \"race\", \"std\"]", "target": 4894038637245960899, "profile": 15657897354478470176, "path": 7920039204278319934, "deps": [[5103565458935487, "futures_io", false, 3311078316949409037], [189982446159473706, "parking", false, 5442620389382704768], [1906322745568073236, "pin_project_lite", false, 377786499476956345], [7620660491849607393, "futures_core", false, 4949676933428321037], [12285238697122577036, "fastrand", false, 1882138737799791459]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-lite-d21a3f0973654012\\dep-lib-futures_lite", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}