{"rustc": 12488743700189009532, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 9153117470745510947, "deps": [[2671782512663819132, "tauri_utils", false, 5376160995092444840], [3060637413840920116, "proc_macro2", false, 6584666464481727759], [3150220818285335163, "url", false, 7634324234670742032], [4899080583175475170, "semver", false, 3007983211784184934], [4974441333307933176, "syn", false, 11021406926825034584], [7170110829644101142, "json_patch", false, 4329366685579714835], [7392050791754369441, "ico", false, 11948169874962656332], [8319709847752024821, "uuid", false, 12507610950001466546], [9556762810601084293, "brotli", false, 1253535388110017513], [9689903380558560274, "serde", false, 6420858207137877299], [9857275760291862238, "sha2", false, 6176870887963913665], [10806645703491011684, "thiserror", false, 14032229823463785456], [12687914511023397207, "png", false, 6429730231949409520], [13077212702700853852, "base64", false, 16086620378069311016], [15367738274754116744, "serde_json", false, 14282978953253237938], [15622660310229662834, "walkdir", false, 13774418868814605573], [17990358020177143287, "quote", false, 17007783491292180124]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-f426098206e9266d\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}