{"rustc": 12488743700189009532, "features": "[\"aes\", \"alloc\", \"default\", \"getrandom\", \"rand_core\"]", "declared_features": "[\"aes\", \"alloc\", \"arrayvec\", \"default\", \"getrandom\", \"heapless\", \"rand_core\", \"std\", \"stream\", \"zeroize\"]", "target": 6327482228044654328, "profile": 15657897354478470176, "path": 16373456618508262592, "deps": [[5822136307240319171, "ctr", false, 1824852404242957083], [7916416211798676886, "cipher", false, 13512874754309562015], [17003143334332120809, "subtle", false, 12027079904136647321], [17625407307438784893, "aes", false, 3627674842304944052], [17797166225172937111, "aead", false, 15463570332082921387], [18030706926766528332, "ghash", false, 7906481217676209256]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\aes-gcm-d955f0e0847014e7\\dep-lib-aes_gcm", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}