{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"std\", \"zeroize\"]", "target": 6545267055209840233, "profile": 15657897354478470176, "path": 7647104166654120677, "deps": [[10592532043434842480, "polyval", false, 2905980457440788476], [13927846409374511869, "opaque_debug", false, 14477325745864395449]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ghash-e3f03049682f22d0\\dep-lib-ghash", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}