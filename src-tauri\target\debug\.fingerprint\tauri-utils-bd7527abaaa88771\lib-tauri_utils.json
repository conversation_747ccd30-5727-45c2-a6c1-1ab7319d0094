{"rustc": 12488743700189009532, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 5437578514626747675, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 8957199841052230058], [3150220818285335163, "url", false, 6062674917897409888], [3191507132440681679, "serde_untagged", false, 12503068862879957042], [4071963112282141418, "serde_with", false, 13646071585371153607], [4899080583175475170, "semver", false, 5668081792804345827], [5986029879202738730, "log", false, 6744974836633744950], [6606131838865521726, "ctor", false, 14932343478959186418], [7170110829644101142, "json_patch", false, 7147162626616945516], [8319709847752024821, "uuid", false, 6662348143637198546], [9010263965687315507, "http", false, 3514203428925704760], [9451456094439810778, "regex", false, 10818032451952740065], [9556762810601084293, "brotli", false, 1253535388110017513], [9689903380558560274, "serde", false, 12662985874143964422], [10806645703491011684, "thiserror", false, 14032229823463785456], [11989259058781683633, "dunce", false, 1785783859457516269], [13625485746686963219, "anyhow", false, 17047174968382787859], [15367738274754116744, "serde_json", false, 14741482141302888685], [15609422047640926750, "toml", false, 15371399588565499550], [15622660310229662834, "walkdir", false, 13860272010085127058], [15932120279885307830, "memchr", false, 11808295126443545459], [17146114186171651583, "infer", false, 4116678982181171841], [17155886227862585100, "glob", false, 4678781086715643041], [17186037756130803222, "phf", false, 2362009805874705074]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-bd7527abaaa88771\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}