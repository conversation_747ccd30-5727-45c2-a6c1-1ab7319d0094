{"rustc": 12488743700189009532, "features": "[\"display\", \"parse\", \"serde\"]", "declared_features": "[\"default\", \"display\", \"parse\", \"perf\", \"serde\", \"unbounded\", \"unstable-debug\"]", "target": 6238804416149507186, "profile": 3183308796342754505, "path": 5167900157242438867, "deps": [[1188343475734137475, "serde_spanned", false, 4528096737626011406], [4092966635514367252, "toml_datetime", false, 16157699754959260394], [6493259146304816786, "indexmap", false, 17638284145613456672], [7621100166442077884, "winnow", false, 7146092678215798548], [9689903380558560274, "serde", false, 12662985874143964422], [15951765469714418051, "toml_write", false, 9303698836186783116]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\toml_edit-f9d73bee57fc746a\\dep-lib-toml_edit", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}