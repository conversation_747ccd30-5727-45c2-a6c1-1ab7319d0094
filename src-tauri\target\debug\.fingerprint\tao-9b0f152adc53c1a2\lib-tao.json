{"rustc": 12488743700189009532, "features": "[\"rwh_06\", \"x11\"]", "declared_features": "[\"default\", \"rwh_04\", \"rwh_05\", \"rwh_06\", \"serde\", \"x11\"]", "target": 18280640111509826504, "profile": 15657897354478470176, "path": 15268455734618377381, "deps": [[1232198224951696867, "unicode_segmentation", false, 17633753206863097527], [3150220818285335163, "url", false, 6062674917897409888], [3334271191048661305, "windows_version", false, 511316909870963320], [3722963349756955755, "once_cell", false, 3001542277175768057], [4143744114649553716, "rwh_06", false, 5439867478843892220], [4495526598637097934, "parking_lot", false, 9740506884829540050], [4684437522915235464, "libc", false, 8772519399186178916], [5628259161083531273, "windows_core", false, 11162791511167397288], [5986029879202738730, "log", false, 6744974836633744950], [7606335748176206944, "dpi", false, 17045838260048947188], [7896293946984509699, "bitflags", false, 6472482513997909962], [9727213718512686088, "crossbeam_channel", false, 7418506299691198615], [14585479307175734061, "windows", false, 1621046816081544285], [17917672826516349275, "lazy_static", false, 10688309873950042378]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tao-9b0f152adc53c1a2\\dep-lib-tao", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}