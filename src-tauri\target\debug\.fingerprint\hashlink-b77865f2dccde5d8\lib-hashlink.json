{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"serde\", \"serde_impl\"]", "target": 3158588102652511467, "profile": 15657897354478470176, "path": 3629747393713087953, "deps": [[13018563866916002725, "hashbrown", false, 3911351943101043638]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashlink-b77865f2dccde5d8\\dep-lib-hashlink", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}