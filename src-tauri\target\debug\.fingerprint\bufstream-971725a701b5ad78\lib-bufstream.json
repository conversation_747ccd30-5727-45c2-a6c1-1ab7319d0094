{"rustc": 12488743700189009532, "features": "[\"default\"]", "declared_features": "[\"default\", \"futures\", \"tokio\", \"tokio-io\"]", "target": 13884101855141719971, "profile": 15657897354478470176, "path": 14681963908788622734, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bufstream-971725a701b5ad78\\dep-lib-bufstream", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}