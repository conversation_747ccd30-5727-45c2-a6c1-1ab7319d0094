{"rustc": 12488743700189009532, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 7178447759297261249], [5876978821129289074, "build_script_build", false, 1756275908801494194]], "local": [{"RerunIfChanged": {"output": "debug\\build\\yaugment-11f514c5db0b7167\\output", "paths": ["tauri.conf.json", "capabilities", "icons\\gzh.jpg"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 0, "compile_kind": 0}