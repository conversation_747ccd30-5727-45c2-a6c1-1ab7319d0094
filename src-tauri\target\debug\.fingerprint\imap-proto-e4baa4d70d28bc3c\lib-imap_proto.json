{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 5516988783425875118, "profile": 15657897354478470176, "path": 5260861513023114107, "deps": [[10578342194672938417, "nom", false, 10235370106716711013]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\imap-proto-e4baa4d70d28bc3c\\dep-lib-imap_proto", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}