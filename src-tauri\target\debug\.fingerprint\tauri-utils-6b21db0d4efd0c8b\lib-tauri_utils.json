{"rustc": 12488743700189009532, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 5437578514626747675, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 6651276197973965228], [3060637413840920116, "proc_macro2", false, 6584666464481727759], [3150220818285335163, "url", false, 7634324234670742032], [3191507132440681679, "serde_untagged", false, 11401863470081109637], [4071963112282141418, "serde_with", false, 17423008279953333137], [4899080583175475170, "semver", false, 3007983211784184934], [5986029879202738730, "log", false, 15515565883962480306], [6606131838865521726, "ctor", false, 14932343478959186418], [6913375703034175521, "schemars", false, 2990764407818227964], [7170110829644101142, "json_patch", false, 4329366685579714835], [8319709847752024821, "uuid", false, 12507610950001466546], [9010263965687315507, "http", false, 3514203428925704760], [9451456094439810778, "regex", false, 11204436105358861165], [9556762810601084293, "brotli", false, 1253535388110017513], [9689903380558560274, "serde", false, 6420858207137877299], [10806645703491011684, "thiserror", false, 14032229823463785456], [11655476559277113544, "cargo_metadata", false, 12118511293442062615], [11989259058781683633, "dunce", false, 1785783859457516269], [13625485746686963219, "anyhow", false, 17047174968382787859], [14232843520438415263, "html5ever", false, 2241959724530748508], [15088007382495681292, "kuchiki", false, 2664066470279169730], [15367738274754116744, "serde_json", false, 14282978953253237938], [15609422047640926750, "toml", false, 12566317272855078969], [15622660310229662834, "walkdir", false, 13774418868814605573], [15932120279885307830, "memchr", false, 4600910453888925406], [17146114186171651583, "infer", false, 12848985501162232135], [17155886227862585100, "glob", false, 4678781086715643041], [17186037756130803222, "phf", false, 17659138274871389167], [17990358020177143287, "quote", false, 17007783491292180124]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-6b21db0d4efd0c8b\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}