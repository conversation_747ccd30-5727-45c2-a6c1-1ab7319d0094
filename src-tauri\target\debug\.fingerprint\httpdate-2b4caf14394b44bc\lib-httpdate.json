{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 12509520342503990962, "profile": 15657897354478470176, "path": 3139627772601554767, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\httpdate-2b4caf14394b44bc\\dep-lib-httpdate", "checksum": false}}], "rustflags": ["-C", "target-feature=+crt-static", "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF"], "config": 2069994364910194474, "compile_kind": 0}